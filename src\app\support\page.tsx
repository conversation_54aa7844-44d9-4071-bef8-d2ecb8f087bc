'use client';

import { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import Layout from '@/components/Layout';

interface FAQ {
  question: string;
  answer: string;
  category: string;
}

export default function SupportPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const categories = ['All', 'Getting Started', 'Payments', 'Security', 'Technical', 'Account'];

  const faqs: FAQ[] = [
    {
      question: 'How do I create a BoGuani account?',
      answer: 'Creating a BoGuani account is simple! Just visit our web app or download our mobile app, enter your phone number, verify it with the SMS code we send you, and set up your profile. The entire process takes less than 2 minutes.',
      category: 'Getting Started'
    },
    {
      question: 'Is BoGuani secure for sending money?',
      answer: 'Absolutely! BoGuani uses bank-grade security with end-to-end encryption for all messages and transactions. We partner with regulated financial institutions and comply with all relevant financial regulations. Your money and data are protected with military-grade encryption.',
      category: 'Security'
    },
    {
      question: 'What are the fees for sending money?',
      answer: 'Bo<PERSON>uani offers competitive rates with transparent pricing. Domestic transfers are typically free or have minimal fees, while international transfers have competitive exchange rates. All fees are clearly displayed before you confirm any transaction.',
      category: 'Payments'
    },
    {
      question: 'Can I use BoGuani on multiple devices?',
      answer: 'Yes! BoGuani syncs seamlessly across all your devices. You can use it on your phone, tablet, and computer, and all your messages and transaction history will be synchronized in real-time.',
      category: 'Technical'
    },
    {
      question: 'How do I verify my identity?',
      answer: 'Identity verification is required for enhanced security and higher transaction limits. You can verify your identity by providing a government-issued ID and completing our secure verification process. This typically takes 1-2 business days.',
      category: 'Account'
    },
    {
      question: 'What should I do if I forget my password?',
      answer: 'BoGuani uses phone number-based authentication, so there\'s no traditional password to forget! If you need to regain access to your account, simply use the "Sign In" option and verify your phone number with the SMS code.',
      category: 'Account'
    },
    {
      question: 'Are my messages really private?',
      answer: 'Yes! All messages in BoGuani are protected with end-to-end encryption. This means only you and the person you\'re messaging can read your conversations. Even BoGuani cannot access your message content.',
      category: 'Security'
    },
    {
      question: 'How fast are money transfers?',
      answer: 'Most domestic transfers are instant, while international transfers typically take 1-3 business days depending on the destination country and local banking systems. You\'ll always see the expected delivery time before confirming your transfer.',
      category: 'Payments'
    },
    {
      question: 'Can I cancel a payment after sending it?',
      answer: 'Once a payment is sent and confirmed by the recipient, it cannot be cancelled. However, if there\'s an issue with your payment, you can contact our support team for assistance. For disputed transactions, we have a resolution process in place.',
      category: 'Payments'
    },
    {
      question: 'What devices are supported?',
      answer: 'BoGuani is available on iOS (iPhone and iPad), Android phones and tablets, and as a web application that works on any modern browser. We also offer desktop apps for Windows, macOS, and Linux.',
      category: 'Technical'
    }
  ];

  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = searchQuery === '' || 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen text-white" style={{
      background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)',
      fontFamily: 'Montserrat, sans-serif'
    }}>
      <div className="relative min-h-screen" style={{
        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)',
        backgroundImage: 'radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)'
      }}>
        {/* Navigation */}
        <nav className="fixed w-full z-50 bg-gradient-to-r from-black/80 to-purple-900/80 backdrop-blur-md border-b border-yellow-500/20">
          <div className="container mx-auto px-6 py-4 flex justify-between items-center">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <div className="text-yellow-500 text-3xl mr-3">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                  BoGuani
                </span>
              </Link>
            </div>
            <div className="flex space-x-6">
              <Link href="/" className="hover:text-yellow-400 transition-colors">Home</Link>
              <Link href="/downloads" className="hover:text-yellow-400 transition-colors">Downloads</Link>
              <Link href="/contact" className="hover:text-yellow-400 transition-colors">Contact</Link>
              <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-4 py-2 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all">
                Open Web App
              </Link>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <div className="pt-24 pb-16 px-6">
          <div className="container mx-auto max-w-6xl">
            {/* Header */}
            <div className="text-center mb-16">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                <i className="fas fa-life-ring text-3xl text-purple-900"></i>
              </div>
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                Support Center
              </h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Find answers to your questions and get help with BoGuani. We&apos;re here to help you every step of the way.
              </p>
            </div>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto mb-12">
              <div className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search for help articles..."
                  className="w-full px-6 py-4 pl-14 bg-purple-900/50 border border-yellow-400/30 rounded-full text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400 text-lg"
                />
                <i className="fas fa-search absolute left-5 top-1/2 transform -translate-y-1/2 text-yellow-400 text-xl"></i>
              </div>
            </div>

            {/* Category Filter */}
            <div className="flex justify-center mb-12">
              <div className="flex flex-wrap gap-3">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-6 py-3 rounded-full font-semibold transition-all ${
                      selectedCategory === category
                        ? 'bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900'
                        : 'bg-purple-900/50 text-gray-300 hover:text-yellow-400 border border-yellow-400/30'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
              <Link href="/contact" className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20 text-center hover:border-yellow-500/40 transition-all group">
                <i className="fas fa-envelope text-4xl text-yellow-400 mb-4 group-hover:scale-110 transition-transform"></i>
                <h3 className="text-xl font-bold mb-2 text-yellow-200">Contact Support</h3>
                <p className="text-gray-300">Get personalized help from our team</p>
              </Link>

              <Link href="/guides" className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20 text-center hover:border-yellow-500/40 transition-all group">
                <i className="fas fa-book text-4xl text-yellow-400 mb-4 group-hover:scale-110 transition-transform"></i>
                <h3 className="text-xl font-bold mb-2 text-yellow-200">User Guides</h3>
                <p className="text-gray-300">Step-by-step tutorials and guides</p>
              </Link>

              <Link href="/auth" className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20 text-center hover:border-yellow-500/40 transition-all group">
                <i className="fas fa-rocket text-4xl text-yellow-400 mb-4 group-hover:scale-110 transition-transform"></i>
                <h3 className="text-xl font-bold mb-2 text-yellow-200">Get Started</h3>
                <p className="text-gray-300">Start using BoGuani right now</p>
              </Link>
            </div>

            {/* FAQ Section */}
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold mb-8 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent text-center">
                Frequently Asked Questions
              </h2>

              {filteredFAQs.length > 0 ? (
                <div className="space-y-4">
                  {filteredFAQs.map((faq, index) => (
                    <motion.div
                      key={index}
                      className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg rounded-2xl border border-yellow-500/20 overflow-hidden"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                    >
                      <button
                        className="w-full p-6 text-left flex justify-between items-center hover:bg-yellow-400/10 transition-all"
                        onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
                      >
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-yellow-200 mb-1">{faq.question}</h3>
                          <span className="text-xs text-yellow-400 bg-yellow-400/20 px-2 py-1 rounded-full">
                            {faq.category}
                          </span>
                        </div>
                        <i className={`fas fa-chevron-${openFAQ === index ? 'up' : 'down'} text-yellow-400 ml-4`}></i>
                      </button>
                      
                      {openFAQ === index && (
                        <motion.div
                          className="px-6 pb-6"
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <div className="border-t border-yellow-500/20 pt-4">
                            <p className="text-gray-300 leading-relaxed">{faq.answer}</p>
                          </div>
                        </motion.div>
                      )}
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-16">
                  <i className="fas fa-search text-6xl text-gray-500 mb-6"></i>
                  <h3 className="text-2xl font-bold text-gray-400 mb-4">No results found</h3>
                  <p className="text-gray-500 mb-8">Try adjusting your search terms or browse our categories above.</p>
                  <button 
                    onClick={() => {
                      setSearchQuery('');
                      setSelectedCategory('All');
                    }}
                    className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all"
                  >
                    Clear Search
                  </button>
                </div>
              )}
            </div>

            {/* Still Need Help */}
            <div className="text-center mt-16">
              <div className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 max-w-2xl mx-auto">
                <h3 className="text-2xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">Still Need Help?</h3>
                <p className="text-gray-300 mb-6">Can&apos;t find what you&apos;re looking for? Our support team is here to help you 24/7.</p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/contact" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all">
                    <i className="fas fa-envelope mr-2"></i>
                    Contact Support
                  </Link>
                  <Link href="/guides" className="border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all">
                    <i className="fas fa-book mr-2"></i>
                    Browse Guides
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
