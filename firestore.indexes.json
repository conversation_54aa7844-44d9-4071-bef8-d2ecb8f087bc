{"indexes": [{"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "chatId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "chats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participants", "arrayConfig": "CONTAINS"}, {"fieldPath": "lastMessageTime", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "username", "order": "ASCENDING"}]}, {"collectionGroup": "contacts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}], "fieldOverrides": []}