# BoGuani - Messenger of Value

**Where Words Carry Worth** - Experience the future of value-based communication with end-to-end encryption and instant transfers.

## 🚀 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: Font Awesome
- **Fonts**: Montserrat (Google Fonts)

## 🎨 Features

### Core Features
- **End-to-End Encryption**: Military-grade security for all messages and transactions
- **Instant Transfers**: Send money as easily as sending a text message
- **Group Payments**: Split bills and manage shared expenses
- **Cross-Platform**: Available on web, iOS, Android, and desktop
- **Real-time Messaging**: Live chat with delivery confirmations

### Pages Included
- **Homepage** (`/`) - Beautiful landing page with features showcase
- **Authentication** (`/auth`) - Multi-step phone verification flow
- **Chat Interface** (`/chat`) - Full messaging experience with mock data
- **Downloads** (`/downloads`) - Platform-specific download options
- **Support Center** (`/support`) - FAQ with search and categories
- **Contact Form** (`/contact`) - Working contact form with validation
- **User Guides** (`/guides`) - Step-by-step tutorials
- **Security Info** (`/security`) - Security features and certifications
- **API Docs** (`/api`) - Developer documentation
- **Privacy Policy** (`/privacy`) - Comprehensive privacy information
- **Terms of Service** (`/terms`) - Legal terms and conditions

## 🎯 Design System

### Colors
- **Primary Purple**: `#2D1B4E`
- **Secondary Purple**: `#4A1A5C`
- **Dark Base**: `#1E1E24`
- **Gold Accent**: `#D4AF37`
- **Light Gold**: `#F2D675`

### Typography
- **Font Family**: Montserrat (300, 400, 500, 600, 700)
- **Gradient Text**: Gold gradient for headings and CTAs
- **Consistent Hierarchy**: Proper font sizes and weights

### Effects
- **Glass Morphism**: Backdrop blur with opacity
- **Gradient Backgrounds**: Multi-layered purple gradients
- **Smooth Animations**: Framer Motion for page transitions
- **Hover Effects**: Scale and color transitions

## 🛠️ Development

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

### Development Server
The application runs on `http://localhost:3000`

### Project Structure
```
src/
├── app/
│   ├── layout.tsx          # Root layout with fonts and metadata
│   ├── page.tsx            # Homepage
│   ├── globals.css         # Global styles
│   ├── auth/page.tsx       # Authentication flow
│   ├── chat/page.tsx       # Chat interface
│   ├── downloads/page.tsx  # Download hub
│   ├── support/page.tsx    # Support center
│   ├── contact/page.tsx    # Contact form
│   ├── guides/page.tsx     # User guides
│   ├── security/page.tsx   # Security information
│   ├── api/page.tsx        # API documentation
│   ├── privacy/page.tsx    # Privacy policy
│   └── terms/page.tsx      # Terms of service
├── components/             # Reusable components (future)
└── lib/                   # Utility functions (future)
```

## 📱 Features Implementation

### Authentication Flow
1. **Phone Verification**: Enter phone number with country code
2. **OTP Confirmation**: 6-digit SMS verification
3. **Profile Creation**: Name and username setup
4. **Local Storage**: User data persistence

### Chat Interface
- **Mock Data**: Sample conversations and contacts
- **Message Types**: Text messages and payment confirmations
- **Real-time UI**: Live typing indicators and status updates
- **Payment Integration**: Send money through chat interface

### Responsive Design
- **Mobile First**: Optimized for mobile devices
- **Tablet Support**: Proper layout for tablets
- **Desktop Experience**: Full-featured desktop interface
- **Cross-browser**: Compatible with modern browsers

## 🔒 Security Features

- **End-to-End Encryption**: All messages encrypted on device
- **Zero Knowledge**: Server cannot access message content
- **Secure Storage**: Encrypted local data storage
- **Authentication**: Phone-based verification system
- **Payment Security**: PCI DSS compliant payment processing

## 🎨 Customization

### Theme Colors
Update colors in `tailwind.config.ts` and component styles.

### Fonts
Change font family in `src/app/layout.tsx` and `globals.css`.

### Animations
Modify Framer Motion animations in individual components.

## 📦 Deployment

### Vercel (Recommended)
```bash
# Deploy to Vercel
npx vercel

# Or connect GitHub repository to Vercel dashboard
```

### Other Platforms
- **Netlify**: Connect GitHub repository
- **Railway**: Deploy with railway CLI
- **Heroku**: Use heroku buildpack for Node.js

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is proprietary software. All rights reserved.

## 📞 Support

- **Email**: <EMAIL>
- **Security**: <EMAIL>
- **Partnerships**: <EMAIL>

---

**BoGuani** - Messenger of Value  
*"Speak Gold. Share Value."*
