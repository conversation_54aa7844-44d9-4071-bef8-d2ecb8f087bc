@echo off
echo 🚀 Deploying BoGuani to Heroku...

REM Check if Heroku CLI is installed
heroku --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Heroku CLI not found. Please install it first:
    echo    https://devcenter.heroku.com/articles/heroku-cli
    pause
    exit /b 1
)

REM Check if user is logged in to Heroku
heroku auth:whoami >nul 2>&1
if errorlevel 1 (
    echo 🔐 Please login to Hero<PERSON> first:
    heroku login
)

REM Get app name from user
set /p APP_NAME="Enter your Heroku app name (or press Enter for 'boguani-messenger'): "
if "%APP_NAME%"=="" set APP_NAME=boguani-messenger

echo 📱 Creating Heroku app: %APP_NAME%

REM Create Heroku app
heroku create %APP_NAME%

REM Set buildpack
heroku buildpacks:set heroku/nodejs --app %APP_NAME%

echo ⚙️  Setting environment variables...

REM Set environment variables
heroku config:set NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyBqMCY_vblZIApBW5I6aShR1iVQIUtnXJ0 --app %APP_NAME%
heroku config:set NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=chatpay-4922e.firebaseapp.com --app %APP_NAME%
heroku config:set NEXT_PUBLIC_FIREBASE_PROJECT_ID=chatpay-4922e --app %APP_NAME%
heroku config:set NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=chatpay-4922e.appspot.com --app %APP_NAME%
heroku config:set NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=211600276697 --app %APP_NAME%
heroku config:set NEXT_PUBLIC_FIREBASE_APP_ID=1:211600276697:web:a94ed4b6baaf7a654492c8 --app %APP_NAME%
heroku config:set NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-GGBK4R7EFV --app %APP_NAME%
heroku config:set NODE_ENV=production --app %APP_NAME%
heroku config:set RATE_LIMIT_MAX=100 --app %APP_NAME%
heroku config:set RATE_LIMIT_WINDOW=900000 --app %APP_NAME%

echo 🔐 Setting secure keys...
heroku config:set JWT_SECRET=boguani-jwt-secret-production-2024 --app %APP_NAME%
heroku config:set NEXTAUTH_SECRET=boguani-nextauth-secret-production-2024 --app %APP_NAME%
heroku config:set ENCRYPTION_KEY=boguani-encryption-key-production-2024 --app %APP_NAME%
heroku config:set MESSAGE_ENCRYPTION_KEY=boguani-message-encryption-production-2024 --app %APP_NAME%
heroku config:set PLAID_ENCRYPTION_KEY=boguani-plaid-encryption-production-2024 --app %APP_NAME%

REM Set app URL
heroku config:set NEXT_PUBLIC_APP_URL=https://%APP_NAME%.herokuapp.com --app %APP_NAME%

echo 📦 Deploying to Heroku...

REM Add git remote
heroku git:remote -a %APP_NAME%

REM Deploy
git add .
git commit -m "Deploy BoGuani to Heroku"
git push heroku main

echo ✅ Deployment complete!
echo 🌐 Your app is available at: https://%APP_NAME%.herokuapp.com
echo.
echo 📋 Next steps:
echo 1. Go to Firebase Console and add your Heroku domain to authorized domains
echo 2. Enable Phone authentication in Firebase
echo 3. Set up Plaid credentials if needed:
echo    heroku config:set PLAID_CLIENT_ID=your_client_id --app %APP_NAME%
echo    heroku config:set PLAID_SECRET=your_secret --app %APP_NAME%
echo.
echo 🎉 BoGuani is now live on Heroku!

REM Open the app
set /p OPEN_APP="Open the app in browser? (y/n): "
if /i "%OPEN_APP%"=="y" heroku open --app %APP_NAME%

pause
