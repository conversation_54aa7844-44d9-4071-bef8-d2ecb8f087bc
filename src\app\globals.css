@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --dark-purple: #2D1B4E;
  --medium-purple: #3D2A5F;
  --light-purple: #4E3A70;
  --dark-gray: #1E1E24;
  --medium-gray: #2D2D34;
  --gold: #D4AF37;
  --gold-light: #F2D675;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-sans), system-ui, sans-serif;
  background: linear-gradient(135deg, var(--dark-gray) 0%, var(--dark-purple) 100%);
  color: white;
  min-height: 100vh;
}

/* Custom utility classes */
.gold-gradient {
  background: linear-gradient(90deg, var(--gold) 0%, var(--gold-light) 50%, var(--gold) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.gold-border {
  border: 2px solid transparent;
  background: linear-gradient(var(--dark-purple), var(--dark-purple)) padding-box,
              linear-gradient(90deg, var(--gold), var(--gold-light)) border-box;
}

/* Component styles */
.feature-card {
  transition: all 0.3s ease-in-out;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
}

.btn-hover {
  transition: all 0.3s ease-in-out;
}

.btn-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
}

/* Hero pattern background */
.hero-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  font-family: inherit;
}
