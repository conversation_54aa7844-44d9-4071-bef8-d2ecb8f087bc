'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface User {
  id: string;
  name: string;
  username: string;
  phone: string;
  isOnline: boolean;
  lastSeen?: string;
  avatar?: string;
  status?: string;
}

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentUser: User | null;
  bankBalance: number;
  connectedBank: string;
}

type SettingsTab = 'profile' | 'chat' | 'banking' | 'security' | 'notifications' | 'privacy' | 'appearance' | 'advanced';

export default function SettingsModal({ isOpen, onClose, currentUser, bankBalance, connectedBank }: SettingsModalProps) {
  const [activeTab, setActiveTab] = useState<SettingsTab>('profile');
  const [profileData, setProfileData] = useState({
    name: currentUser?.name || '',
    username: currentUser?.username || '',
    phone: currentUser?.phone || '',
    email: '<EMAIL>',
    status: currentUser?.status || '',
    bio: 'Messenger of Value',
    avatar: currentUser?.avatar || ''
  });

  const [chatSettings, setChatSettings] = useState({
    enterToSend: true,
    readReceipts: true,
    typingIndicators: true,
    lastSeen: true,
    mediaAutoDownload: true,
    soundEnabled: true,
    vibrationEnabled: true,
    messagePreview: true,
    groupNotifications: true,
    archiveChats: false
  });

  const [bankingSettings, setBankingSettings] = useState({
    instantTransfers: true,
    paymentNotifications: true,
    transactionLimits: {
      daily: 5000,
      monthly: 25000
    },
    autoSave: false,
    savingsGoal: 1000,
    requirePinForPayments: true,
    biometricAuth: true
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: true,
    biometricLogin: true,
    sessionTimeout: 30,
    deviceVerification: true,
    encryptionLevel: 'military',
    backupMessages: true,
    screenLock: true,
    incognitoMode: false
  });

  const [notificationSettings, setNotificationSettings] = useState({
    pushNotifications: true,
    emailNotifications: false,
    smsNotifications: false,
    soundAlerts: true,
    vibration: true,
    showPreviews: true,
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '07:00'
    },
    priorityContacts: []
  });

  const [privacySettings, setPrivacySettings] = useState({
    profileVisibility: 'contacts',
    lastSeenVisibility: 'contacts',
    statusVisibility: 'everyone',
    readReceiptSharing: true,
    blockUnknownNumbers: false,
    dataCollection: false,
    analyticsSharing: false,
    locationSharing: false
  });

  const [appearanceSettings, setAppearanceSettings] = useState({
    theme: 'dark',
    accentColor: 'gold',
    fontSize: 'medium',
    chatWallpaper: 'default',
    bubbleStyle: 'transparent',
    animationsEnabled: true,
    compactMode: false,
    highContrast: false
  });

  const tabs = [
    { id: 'profile', name: 'Profile', icon: 'fas fa-user' },
    { id: 'chat', name: 'Chat', icon: 'fas fa-comments' },
    { id: 'banking', name: 'Banking', icon: 'fas fa-university' },
    { id: 'security', name: 'Security', icon: 'fas fa-shield-alt' },
    { id: 'notifications', name: 'Notifications', icon: 'fas fa-bell' },
    { id: 'privacy', name: 'Privacy', icon: 'fas fa-lock' },
    { id: 'appearance', name: 'Appearance', icon: 'fas fa-palette' },
    { id: 'advanced', name: 'Advanced', icon: 'fas fa-cogs' }
  ];

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div 
        className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div 
          className="bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-lg rounded-2xl shadow-2xl w-full max-w-6xl h-[90vh] border border-gray-600/50 professional-shadow overflow-hidden"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex h-full">
            {/* Sidebar */}
            <div className="w-64 bg-gradient-to-b from-gray-900/90 to-gray-800/90 border-r border-gray-600/30 p-6">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-2xl font-bold text-white gold-gradient">Settings</h2>
                <button 
                  onClick={onClose}
                  className="text-gray-400 hover:text-white transition-colors p-2 rounded-full hover:bg-gray-700/50"
                >
                  <i className="fas fa-times text-xl"></i>
                </button>
              </div>
              
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as SettingsTab)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 border border-yellow-400/30 text-yellow-400'
                        : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                    }`}
                  >
                    <i className={`${tab.icon} text-lg`}></i>
                    <span className="font-medium">{tab.name}</span>
                  </button>
                ))}
              </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1 overflow-y-auto">
              <div className="p-8">
                {/* Profile Settings */}
                {activeTab === 'profile' && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-8"
                  >
                    <div>
                      <h3 className="text-2xl font-bold text-white mb-2">Profile Settings</h3>
                      <p className="text-gray-400">Manage your personal information and profile appearance</p>
                    </div>

                    {/* Profile Picture */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-4">Profile Picture</h4>
                      <div className="flex items-center space-x-6">
                        <div className="w-20 h-20 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-gray-900 font-bold text-2xl professional-shadow">
                          {profileData.name.split(' ').map((n: string) => n[0]).join('')}
                        </div>
                        <div className="space-y-3">
                          <button className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-6 py-2 rounded-lg font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300">
                            Upload Photo
                          </button>
                          <button className="block text-gray-400 hover:text-white transition-colors text-sm">
                            Remove Photo
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Personal Information */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Personal Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Full Name</label>
                          <input
                            type="text"
                            value={profileData.name}
                            onChange={(e) => setProfileData({...profileData, name: e.target.value})}
                            className="w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Username</label>
                          <input
                            type="text"
                            value={profileData.username}
                            onChange={(e) => setProfileData({...profileData, username: e.target.value})}
                            className="w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Phone Number</label>
                          <input
                            type="tel"
                            value={profileData.phone}
                            onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                            className="w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                          <input
                            type="email"
                            value={profileData.email}
                            onChange={(e) => setProfileData({...profileData, email: e.target.value})}
                            className="w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Status & Bio */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Status & Bio</h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Status Message</label>
                          <input
                            type="text"
                            value={profileData.status}
                            onChange={(e) => setProfileData({...profileData, status: e.target.value})}
                            placeholder="Available for chat"
                            className="w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Bio</label>
                          <textarea
                            value={profileData.bio}
                            onChange={(e) => setProfileData({...profileData, bio: e.target.value})}
                            placeholder="Tell others about yourself..."
                            rows={3}
                            className="w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none resize-none"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Save Button */}
                    <div className="flex justify-end">
                      <button className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-8 py-3 rounded-xl font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow">
                        <i className="fas fa-save mr-2"></i>
                        Save Changes
                      </button>
                    </div>
                  </motion.div>
                )}

                {/* Chat Settings */}
                {activeTab === 'chat' && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-8"
                  >
                    <div>
                      <h3 className="text-2xl font-bold text-white mb-2">Chat Settings</h3>
                      <p className="text-gray-400">Customize your messaging experience and preferences</p>
                    </div>

                    {/* Message Behavior */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Message Behavior</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Enter to Send</h5>
                            <p className="text-gray-400 text-sm">Press Enter to send messages (Shift+Enter for new line)</p>
                          </div>
                          <button
                            onClick={() => setChatSettings({...chatSettings, enterToSend: !chatSettings.enterToSend})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              chatSettings.enterToSend ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              chatSettings.enterToSend ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Read Receipts</h5>
                            <p className="text-gray-400 text-sm">Show when you&apos;ve read messages</p>
                          </div>
                          <button
                            onClick={() => setChatSettings({...chatSettings, readReceipts: !chatSettings.readReceipts})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              chatSettings.readReceipts ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              chatSettings.readReceipts ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Typing Indicators</h5>
                            <p className="text-gray-400 text-sm">Show when you&apos;re typing to others</p>
                          </div>
                          <button
                            onClick={() => setChatSettings({...chatSettings, typingIndicators: !chatSettings.typingIndicators})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              chatSettings.typingIndicators ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              chatSettings.typingIndicators ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Last Seen</h5>
                            <p className="text-gray-400 text-sm">Show your last seen status to contacts</p>
                          </div>
                          <button
                            onClick={() => setChatSettings({...chatSettings, lastSeen: !chatSettings.lastSeen})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              chatSettings.lastSeen ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              chatSettings.lastSeen ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Media & Files */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Media & Files</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Auto-Download Media</h5>
                            <p className="text-gray-400 text-sm">Automatically download photos and videos</p>
                          </div>
                          <button
                            onClick={() => setChatSettings({...chatSettings, mediaAutoDownload: !chatSettings.mediaAutoDownload})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              chatSettings.mediaAutoDownload ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              chatSettings.mediaAutoDownload ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Notifications */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Chat Notifications</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Sound Notifications</h5>
                            <p className="text-gray-400 text-sm">Play sound for new messages</p>
                          </div>
                          <button
                            onClick={() => setChatSettings({...chatSettings, soundEnabled: !chatSettings.soundEnabled})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              chatSettings.soundEnabled ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              chatSettings.soundEnabled ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Vibration</h5>
                            <p className="text-gray-400 text-sm">Vibrate for new messages</p>
                          </div>
                          <button
                            onClick={() => setChatSettings({...chatSettings, vibrationEnabled: !chatSettings.vibrationEnabled})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              chatSettings.vibrationEnabled ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              chatSettings.vibrationEnabled ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Message Previews</h5>
                            <p className="text-gray-400 text-sm">Show message content in notifications</p>
                          </div>
                          <button
                            onClick={() => setChatSettings({...chatSettings, messagePreview: !chatSettings.messagePreview})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              chatSettings.messagePreview ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              chatSettings.messagePreview ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Banking Settings */}
                {activeTab === 'banking' && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-8"
                  >
                    <div>
                      <h3 className="text-2xl font-bold text-white mb-2">Banking & Payments</h3>
                      <p className="text-gray-400">Manage your financial settings and payment preferences</p>
                    </div>

                    {/* Connected Accounts */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Connected Bank Accounts</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-xl border border-green-400/20">
                          <div className="flex items-center space-x-4">
                            <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                              <i className="fas fa-university text-white"></i>
                            </div>
                            <div>
                              <h5 className="text-white font-medium">{connectedBank}</h5>
                              <p className="text-green-400 text-sm">Primary Account • Verified</p>
                              <p className="text-gray-400 text-sm">Balance: ${bankBalance.toLocaleString()}</p>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <button className="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-700/50">
                              <i className="fas fa-edit"></i>
                            </button>
                            <button className="text-gray-400 hover:text-red-400 transition-colors p-2 rounded-lg hover:bg-gray-700/50">
                              <i className="fas fa-trash"></i>
                            </button>
                          </div>
                        </div>

                        <button className="w-full p-4 border-2 border-dashed border-gray-600 rounded-xl text-gray-400 hover:text-white hover:border-yellow-400 transition-all duration-300">
                          <i className="fas fa-plus mr-2"></i>
                          Add New Bank Account
                        </button>
                      </div>
                    </div>

                    {/* Payment Settings */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Payment Settings</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Instant Transfers</h5>
                            <p className="text-gray-400 text-sm">Enable instant money transfers (small fee may apply)</p>
                          </div>
                          <button
                            onClick={() => setBankingSettings({...bankingSettings, instantTransfers: !bankingSettings.instantTransfers})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              bankingSettings.instantTransfers ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              bankingSettings.instantTransfers ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Payment Notifications</h5>
                            <p className="text-gray-400 text-sm">Get notified for all payment activities</p>
                          </div>
                          <button
                            onClick={() => setBankingSettings({...bankingSettings, paymentNotifications: !bankingSettings.paymentNotifications})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              bankingSettings.paymentNotifications ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              bankingSettings.paymentNotifications ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Require PIN for Payments</h5>
                            <p className="text-gray-400 text-sm">Require PIN verification for all payments</p>
                          </div>
                          <button
                            onClick={() => setBankingSettings({...bankingSettings, requirePinForPayments: !bankingSettings.requirePinForPayments})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              bankingSettings.requirePinForPayments ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              bankingSettings.requirePinForPayments ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Biometric Authentication</h5>
                            <p className="text-gray-400 text-sm">Use fingerprint/face ID for payments</p>
                          </div>
                          <button
                            onClick={() => setBankingSettings({...bankingSettings, biometricAuth: !bankingSettings.biometricAuth})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              bankingSettings.biometricAuth ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              bankingSettings.biometricAuth ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Transaction Limits */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Transaction Limits</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Daily Limit</label>
                          <div className="relative">
                            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400">$</span>
                            <input
                              type="number"
                              value={bankingSettings.transactionLimits.daily}
                              onChange={(e) => setBankingSettings({
                                ...bankingSettings,
                                transactionLimits: {...bankingSettings.transactionLimits, daily: parseInt(e.target.value)}
                              })}
                              className="w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"
                            />
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Monthly Limit</label>
                          <div className="relative">
                            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400">$</span>
                            <input
                              type="number"
                              value={bankingSettings.transactionLimits.monthly}
                              onChange={(e) => setBankingSettings({
                                ...bankingSettings,
                                transactionLimits: {...bankingSettings.transactionLimits, monthly: parseInt(e.target.value)}
                              })}
                              className="w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Savings */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Savings & Goals</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Auto-Save</h5>
                            <p className="text-gray-400 text-sm">Automatically save spare change from transactions</p>
                          </div>
                          <button
                            onClick={() => setBankingSettings({...bankingSettings, autoSave: !bankingSettings.autoSave})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              bankingSettings.autoSave ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              bankingSettings.autoSave ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Savings Goal</label>
                          <div className="relative">
                            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400">$</span>
                            <input
                              type="number"
                              value={bankingSettings.savingsGoal}
                              onChange={(e) => setBankingSettings({...bankingSettings, savingsGoal: parseInt(e.target.value)})}
                              className="w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"
                              placeholder="1000"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Security Settings */}
                {activeTab === 'security' && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-8"
                  >
                    <div>
                      <h3 className="text-2xl font-bold text-white mb-2">Security & Privacy</h3>
                      <p className="text-gray-400">Protect your account with advanced security features</p>
                    </div>

                    {/* Authentication */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Authentication</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Two-Factor Authentication</h5>
                            <p className="text-gray-400 text-sm">Add an extra layer of security to your account</p>
                          </div>
                          <button
                            onClick={() => setSecuritySettings({...securitySettings, twoFactorAuth: !securitySettings.twoFactorAuth})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              securitySettings.twoFactorAuth ? 'bg-green-500' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              securitySettings.twoFactorAuth ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Biometric Login</h5>
                            <p className="text-gray-400 text-sm">Use fingerprint or face ID to log in</p>
                          </div>
                          <button
                            onClick={() => setSecuritySettings({...securitySettings, biometricLogin: !securitySettings.biometricLogin})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              securitySettings.biometricLogin ? 'bg-green-500' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              securitySettings.biometricLogin ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Device Verification</h5>
                            <p className="text-gray-400 text-sm">Verify new devices before allowing access</p>
                          </div>
                          <button
                            onClick={() => setSecuritySettings({...securitySettings, deviceVerification: !securitySettings.deviceVerification})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              securitySettings.deviceVerification ? 'bg-green-500' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              securitySettings.deviceVerification ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Session Timeout (minutes)</label>
                          <select
                            value={securitySettings.sessionTimeout}
                            onChange={(e) => setSecuritySettings({...securitySettings, sessionTimeout: parseInt(e.target.value)})}
                            className="w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"
                          >
                            <option value={15}>15 minutes</option>
                            <option value={30}>30 minutes</option>
                            <option value={60}>1 hour</option>
                            <option value={120}>2 hours</option>
                            <option value={0}>Never</option>
                          </select>
                        </div>
                      </div>
                    </div>

                    {/* Encryption */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Encryption & Data</h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Encryption Level</label>
                          <select
                            value={securitySettings.encryptionLevel}
                            onChange={(e) => setSecuritySettings({...securitySettings, encryptionLevel: e.target.value})}
                            className="w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"
                          >
                            <option value="standard">Standard (128-bit)</option>
                            <option value="enhanced">Enhanced (256-bit)</option>
                            <option value="military">Military Grade (AES-256)</option>
                          </select>
                          <p className="text-green-400 text-sm mt-2 flex items-center">
                            <i className="fas fa-shield-alt mr-2"></i>
                            Current: Military Grade AES-256 Encryption
                          </p>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Backup Messages</h5>
                            <p className="text-gray-400 text-sm">Securely backup your messages to the cloud</p>
                          </div>
                          <button
                            onClick={() => setSecuritySettings({...securitySettings, backupMessages: !securitySettings.backupMessages})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              securitySettings.backupMessages ? 'bg-green-500' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              securitySettings.backupMessages ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Screen Lock</h5>
                            <p className="text-gray-400 text-sm">Lock app when switching to other apps</p>
                          </div>
                          <button
                            onClick={() => setSecuritySettings({...securitySettings, screenLock: !securitySettings.screenLock})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              securitySettings.screenLock ? 'bg-green-500' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              securitySettings.screenLock ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Incognito Mode</h5>
                            <p className="text-gray-400 text-sm">Hide message previews and disable screenshots</p>
                          </div>
                          <button
                            onClick={() => setSecuritySettings({...securitySettings, incognitoMode: !securitySettings.incognitoMode})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              securitySettings.incognitoMode ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              securitySettings.incognitoMode ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Security Actions */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Security Actions</h4>
                      <div className="space-y-3">
                        <button className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-600/20 to-blue-500/20 rounded-xl border border-blue-400/30 hover:border-blue-400/50 transition-colors">
                          <div className="flex items-center">
                            <i className="fas fa-key text-blue-400 mr-3"></i>
                            <span className="text-white font-medium">Change Password</span>
                          </div>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </button>

                        <button className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-green-600/20 to-green-500/20 rounded-xl border border-green-400/30 hover:border-green-400/50 transition-colors">
                          <div className="flex items-center">
                            <i className="fas fa-mobile-alt text-green-400 mr-3"></i>
                            <span className="text-white font-medium">Manage Devices</span>
                          </div>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </button>

                        <button className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-yellow-600/20 to-yellow-500/20 rounded-xl border border-yellow-400/30 hover:border-yellow-400/50 transition-colors">
                          <div className="flex items-center">
                            <i className="fas fa-history text-yellow-400 mr-3"></i>
                            <span className="text-white font-medium">Login History</span>
                          </div>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </button>

                        <button className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-red-600/20 to-red-500/20 rounded-xl border border-red-400/30 hover:border-red-400/50 transition-colors">
                          <div className="flex items-center">
                            <i className="fas fa-sign-out-alt text-red-400 mr-3"></i>
                            <span className="text-white font-medium">Sign Out All Devices</span>
                          </div>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Notifications Settings */}
                {activeTab === 'notifications' && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-8"
                  >
                    <div>
                      <h3 className="text-2xl font-bold text-white mb-2">Notifications</h3>
                      <p className="text-gray-400">Control how and when you receive notifications</p>
                    </div>

                    {/* General Notifications */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">General</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Push Notifications</h5>
                            <p className="text-gray-400 text-sm">Receive notifications on this device</p>
                          </div>
                          <button
                            onClick={() => setNotificationSettings({...notificationSettings, pushNotifications: !notificationSettings.pushNotifications})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              notificationSettings.pushNotifications ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              notificationSettings.pushNotifications ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Email Notifications</h5>
                            <p className="text-gray-400 text-sm">Receive important updates via email</p>
                          </div>
                          <button
                            onClick={() => setNotificationSettings({...notificationSettings, emailNotifications: !notificationSettings.emailNotifications})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              notificationSettings.emailNotifications ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              notificationSettings.emailNotifications ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Sound Alerts</h5>
                            <p className="text-gray-400 text-sm">Play sound for notifications</p>
                          </div>
                          <button
                            onClick={() => setNotificationSettings({...notificationSettings, soundAlerts: !notificationSettings.soundAlerts})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              notificationSettings.soundAlerts ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              notificationSettings.soundAlerts ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Show Previews</h5>
                            <p className="text-gray-400 text-sm">Show message content in notifications</p>
                          </div>
                          <button
                            onClick={() => setNotificationSettings({...notificationSettings, showPreviews: !notificationSettings.showPreviews})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              notificationSettings.showPreviews ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              notificationSettings.showPreviews ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Quiet Hours */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Quiet Hours</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Enable Quiet Hours</h5>
                            <p className="text-gray-400 text-sm">Mute notifications during specified hours</p>
                          </div>
                          <button
                            onClick={() => setNotificationSettings({
                              ...notificationSettings,
                              quietHours: {...notificationSettings.quietHours, enabled: !notificationSettings.quietHours.enabled}
                            })}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              notificationSettings.quietHours.enabled ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              notificationSettings.quietHours.enabled ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        {notificationSettings.quietHours.enabled && (
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-300 mb-2">Start Time</label>
                              <input
                                type="time"
                                value={notificationSettings.quietHours.start}
                                onChange={(e) => setNotificationSettings({
                                  ...notificationSettings,
                                  quietHours: {...notificationSettings.quietHours, start: e.target.value}
                                })}
                                className="w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-300 mb-2">End Time</label>
                              <input
                                type="time"
                                value={notificationSettings.quietHours.end}
                                onChange={(e) => setNotificationSettings({
                                  ...notificationSettings,
                                  quietHours: {...notificationSettings.quietHours, end: e.target.value}
                                })}
                                className="w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Privacy Settings */}
                {activeTab === 'privacy' && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-8"
                  >
                    <div>
                      <h3 className="text-2xl font-bold text-white mb-2">Privacy</h3>
                      <p className="text-gray-400">Control who can see your information and activity</p>
                    </div>

                    {/* Visibility Settings */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Visibility</h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Profile Visibility</label>
                          <select
                            value={privacySettings.profileVisibility}
                            onChange={(e) => setPrivacySettings({...privacySettings, profileVisibility: e.target.value})}
                            className="w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"
                          >
                            <option value="everyone">Everyone</option>
                            <option value="contacts">My Contacts</option>
                            <option value="nobody">Nobody</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Last Seen</label>
                          <select
                            value={privacySettings.lastSeenVisibility}
                            onChange={(e) => setPrivacySettings({...privacySettings, lastSeenVisibility: e.target.value})}
                            className="w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"
                          >
                            <option value="everyone">Everyone</option>
                            <option value="contacts">My Contacts</option>
                            <option value="nobody">Nobody</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Status Visibility</label>
                          <select
                            value={privacySettings.statusVisibility}
                            onChange={(e) => setPrivacySettings({...privacySettings, statusVisibility: e.target.value})}
                            className="w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"
                          >
                            <option value="everyone">Everyone</option>
                            <option value="contacts">My Contacts</option>
                            <option value="nobody">Nobody</option>
                          </select>
                        </div>
                      </div>
                    </div>

                    {/* Data & Analytics */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Data & Analytics</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Data Collection</h5>
                            <p className="text-gray-400 text-sm">Allow collection of usage data for improvements</p>
                          </div>
                          <button
                            onClick={() => setPrivacySettings({...privacySettings, dataCollection: !privacySettings.dataCollection})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              privacySettings.dataCollection ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              privacySettings.dataCollection ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Analytics Sharing</h5>
                            <p className="text-gray-400 text-sm">Share anonymous analytics data</p>
                          </div>
                          <button
                            onClick={() => setPrivacySettings({...privacySettings, analyticsSharing: !privacySettings.analyticsSharing})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              privacySettings.analyticsSharing ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              privacySettings.analyticsSharing ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Location Sharing</h5>
                            <p className="text-gray-400 text-sm">Allow location sharing in messages</p>
                          </div>
                          <button
                            onClick={() => setPrivacySettings({...privacySettings, locationSharing: !privacySettings.locationSharing})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              privacySettings.locationSharing ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              privacySettings.locationSharing ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Appearance Settings */}
                {activeTab === 'appearance' && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-8"
                  >
                    <div>
                      <h3 className="text-2xl font-bold text-white mb-2">Appearance</h3>
                      <p className="text-gray-400">Customize the look and feel of BoGuani</p>
                    </div>

                    {/* Theme Settings */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Theme</h4>
                      <div className="grid grid-cols-3 gap-4">
                        <button
                          onClick={() => setAppearanceSettings({...appearanceSettings, theme: 'dark'})}
                          className={`p-4 rounded-xl border-2 transition-all ${
                            appearanceSettings.theme === 'dark'
                              ? 'border-yellow-400 bg-yellow-400/10'
                              : 'border-gray-600 hover:border-gray-500'
                          }`}
                        >
                          <div className="w-full h-16 bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg mb-2"></div>
                          <p className="text-white text-sm font-medium">Dark</p>
                        </button>

                        <button
                          onClick={() => setAppearanceSettings({...appearanceSettings, theme: 'light'})}
                          className={`p-4 rounded-xl border-2 transition-all ${
                            appearanceSettings.theme === 'light'
                              ? 'border-yellow-400 bg-yellow-400/10'
                              : 'border-gray-600 hover:border-gray-500'
                          }`}
                        >
                          <div className="w-full h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-2"></div>
                          <p className="text-white text-sm font-medium">Light</p>
                        </button>

                        <button
                          onClick={() => setAppearanceSettings({...appearanceSettings, theme: 'auto'})}
                          className={`p-4 rounded-xl border-2 transition-all ${
                            appearanceSettings.theme === 'auto'
                              ? 'border-yellow-400 bg-yellow-400/10'
                              : 'border-gray-600 hover:border-gray-500'
                          }`}
                        >
                          <div className="w-full h-16 bg-gradient-to-r from-gray-900 via-gray-500 to-gray-100 rounded-lg mb-2"></div>
                          <p className="text-white text-sm font-medium">Auto</p>
                        </button>
                      </div>
                    </div>

                    {/* Customization */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Customization</h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Accent Color</label>
                          <div className="grid grid-cols-6 gap-3">
                            {['gold', 'blue', 'green', 'purple', 'red', 'orange'].map((color) => (
                              <button
                                key={color}
                                onClick={() => setAppearanceSettings({...appearanceSettings, accentColor: color})}
                                className={`w-12 h-12 rounded-full border-2 transition-all ${
                                  appearanceSettings.accentColor === color ? 'border-white scale-110' : 'border-gray-600'
                                } ${
                                  color === 'gold' ? 'bg-yellow-400' :
                                  color === 'blue' ? 'bg-blue-500' :
                                  color === 'green' ? 'bg-green-500' :
                                  color === 'purple' ? 'bg-purple-500' :
                                  color === 'red' ? 'bg-red-500' : 'bg-orange-500'
                                }`}
                              />
                            ))}
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Font Size</label>
                          <select
                            value={appearanceSettings.fontSize}
                            onChange={(e) => setAppearanceSettings({...appearanceSettings, fontSize: e.target.value})}
                            className="w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"
                          >
                            <option value="small">Small</option>
                            <option value="medium">Medium</option>
                            <option value="large">Large</option>
                            <option value="extra-large">Extra Large</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Message Bubble Style</label>
                          <select
                            value={appearanceSettings.bubbleStyle}
                            onChange={(e) => setAppearanceSettings({...appearanceSettings, bubbleStyle: e.target.value})}
                            className="w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"
                          >
                            <option value="transparent">Transparent</option>
                            <option value="solid">Solid</option>
                            <option value="gradient">Gradient</option>
                          </select>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Animations</h5>
                            <p className="text-gray-400 text-sm">Enable smooth animations and transitions</p>
                          </div>
                          <button
                            onClick={() => setAppearanceSettings({...appearanceSettings, animationsEnabled: !appearanceSettings.animationsEnabled})}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              appearanceSettings.animationsEnabled ? 'bg-yellow-400' : 'bg-gray-600'
                            }`}
                          >
                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              appearanceSettings.animationsEnabled ? 'translate-x-6' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Advanced Settings */}
                {activeTab === 'advanced' && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-8"
                  >
                    <div>
                      <h3 className="text-2xl font-bold text-white mb-2">Advanced</h3>
                      <p className="text-gray-400">Advanced settings and developer options</p>
                    </div>

                    {/* Data Management */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Data Management</h4>
                      <div className="space-y-3">
                        <button className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-600/20 to-blue-500/20 rounded-xl border border-blue-400/30 hover:border-blue-400/50 transition-colors">
                          <div className="flex items-center">
                            <i className="fas fa-download text-blue-400 mr-3"></i>
                            <div className="text-left">
                              <span className="text-white font-medium block">Export Data</span>
                              <span className="text-gray-400 text-sm">Download your messages and data</span>
                            </div>
                          </div>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </button>

                        <button className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-green-600/20 to-green-500/20 rounded-xl border border-green-400/30 hover:border-green-400/50 transition-colors">
                          <div className="flex items-center">
                            <i className="fas fa-upload text-green-400 mr-3"></i>
                            <div className="text-left">
                              <span className="text-white font-medium block">Import Data</span>
                              <span className="text-gray-400 text-sm">Import messages from other apps</span>
                            </div>
                          </div>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </button>

                        <button className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-yellow-600/20 to-yellow-500/20 rounded-xl border border-yellow-400/30 hover:border-yellow-400/50 transition-colors">
                          <div className="flex items-center">
                            <i className="fas fa-broom text-yellow-400 mr-3"></i>
                            <div className="text-left">
                              <span className="text-white font-medium block">Clear Cache</span>
                              <span className="text-gray-400 text-sm">Free up storage space</span>
                            </div>
                          </div>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </button>
                      </div>
                    </div>

                    {/* Developer Options */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Developer Options</h4>
                      <div className="space-y-3">
                        <button className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-purple-600/20 to-purple-500/20 rounded-xl border border-purple-400/30 hover:border-purple-400/50 transition-colors">
                          <div className="flex items-center">
                            <i className="fas fa-code text-purple-400 mr-3"></i>
                            <div className="text-left">
                              <span className="text-white font-medium block">API Settings</span>
                              <span className="text-gray-400 text-sm">Configure API endpoints and keys</span>
                            </div>
                          </div>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </button>

                        <button className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-indigo-600/20 to-indigo-500/20 rounded-xl border border-indigo-400/30 hover:border-indigo-400/50 transition-colors">
                          <div className="flex items-center">
                            <i className="fas fa-bug text-indigo-400 mr-3"></i>
                            <div className="text-left">
                              <span className="text-white font-medium block">Debug Logs</span>
                              <span className="text-gray-400 text-sm">View and export debug information</span>
                            </div>
                          </div>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </button>

                        <button className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-teal-600/20 to-teal-500/20 rounded-xl border border-teal-400/30 hover:border-teal-400/50 transition-colors">
                          <div className="flex items-center">
                            <i className="fas fa-flask text-teal-400 mr-3"></i>
                            <div className="text-left">
                              <span className="text-white font-medium block">Beta Features</span>
                              <span className="text-gray-400 text-sm">Enable experimental features</span>
                            </div>
                          </div>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </button>
                      </div>
                    </div>

                    {/* Account Actions */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">Account Actions</h4>
                      <div className="space-y-3">
                        <button className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-orange-600/20 to-orange-500/20 rounded-xl border border-orange-400/30 hover:border-orange-400/50 transition-colors">
                          <div className="flex items-center">
                            <i className="fas fa-user-times text-orange-400 mr-3"></i>
                            <div className="text-left">
                              <span className="text-white font-medium block">Deactivate Account</span>
                              <span className="text-gray-400 text-sm">Temporarily disable your account</span>
                            </div>
                          </div>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </button>

                        <button className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-red-600/20 to-red-500/20 rounded-xl border border-red-400/30 hover:border-red-400/50 transition-colors">
                          <div className="flex items-center">
                            <i className="fas fa-trash text-red-400 mr-3"></i>
                            <div className="text-left">
                              <span className="text-white font-medium block">Delete Account</span>
                              <span className="text-gray-400 text-sm">Permanently delete your account and data</span>
                            </div>
                          </div>
                          <i className="fas fa-chevron-right text-gray-400"></i>
                        </button>
                      </div>
                    </div>

                    {/* App Information */}
                    <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30">
                      <h4 className="text-lg font-semibold text-white mb-6">App Information</h4>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-400">Version</span>
                          <span className="text-white font-medium">1.0.0</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-400">Build</span>
                          <span className="text-white font-medium">2024.01.15</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-400">Platform</span>
                          <span className="text-white font-medium">Web</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-400">Encryption</span>
                          <span className="text-green-400 font-medium flex items-center">
                            <i className="fas fa-shield-alt mr-2"></i>
                            AES-256 Active
                          </span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
