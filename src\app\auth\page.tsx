'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { auth } from '@/lib/firebase';
import { RecaptchaVerifier, signInWithPhoneNumber, ConfirmationResult } from 'firebase/auth';

interface CountryCode {
  code: string;
  country: string;
  flag: string;
}

export default function AuthPage() {
  const router = useRouter();
  const [step, setStep] = useState<'phone' | 'otp' | 'profile'>('phone');
  const [countryCode, setCountryCode] = useState('+1');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otpCode, setOtpCode] = useState('');
  const [name, setName] = useState('');
  const [username, setUsername] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [confirmationResult, setConfirmationResult] = useState<ConfirmationResult | null>(null);
  const [recaptchaVerifier, setRecaptchaVerifier] = useState<RecaptchaVerifier | null>(null);

  const countryCodes: CountryCode[] = [
    { code: '+1', country: 'United States', flag: '🇺🇸' },
    { code: '+1', country: 'Canada', flag: '🇨🇦' },
    { code: '+44', country: 'United Kingdom', flag: '🇬🇧' },
    { code: '+33', country: 'France', flag: '🇫🇷' },
    { code: '+49', country: 'Germany', flag: '🇩🇪' },
    { code: '+81', country: 'Japan', flag: '🇯🇵' },
    { code: '+86', country: 'China', flag: '🇨🇳' },
    { code: '+91', country: 'India', flag: '🇮🇳' },
    { code: '+55', country: 'Brazil', flag: '🇧🇷' },
    { code: '+61', country: 'Australia', flag: '🇦🇺' }
  ];

  // Initialize reCAPTCHA
  useEffect(() => {
    if (typeof window !== 'undefined' && !recaptchaVerifier) {
      const verifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
        size: 'invisible',
        callback: () => {
          console.log('reCAPTCHA solved');
        },
        'expired-callback': () => {
          console.log('reCAPTCHA expired');
        }
      });
      setRecaptchaVerifier(verifier);
    }
  }, [recaptchaVerifier]);

  const sendOTP = async () => {
    if (!phoneNumber.trim()) {
      setError('Please enter your phone number');
      return;
    }

    if (!recaptchaVerifier) {
      setError('reCAPTCHA not initialized. Please refresh the page.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const fullPhoneNumber = countryCode + phoneNumber;
      console.log('Sending SMS to:', fullPhoneNumber);

      // Use Firebase Auth to send SMS
      const confirmation = await signInWithPhoneNumber(auth, fullPhoneNumber, recaptchaVerifier);
      setConfirmationResult(confirmation);
      setStep('otp');
      setLoading(false);
      console.log('SMS sent successfully');

    } catch (error: any) {
      console.error('SMS sending error:', error);
      setError(error.message || 'Failed to send verification code. Please try again.');
      setLoading(false);

      // Reset reCAPTCHA on error
      if (recaptchaVerifier) {
        recaptchaVerifier.clear();
        setRecaptchaVerifier(null);
      }
    }
  };

  const verifyOTP = async () => {
    if (!otpCode.trim() || otpCode.length !== 6) {
      setError('Please enter a valid 6-digit code');
      return;
    }

    if (!confirmationResult) {
      setError('No verification session found. Please request a new code.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Verify the SMS code with Firebase
      const result = await confirmationResult.confirm(otpCode);
      console.log('Phone verification successful:', result.user.uid);

      // Store the Firebase user info
      const firebaseUser = result.user;

      setStep('profile');
      setLoading(false);

    } catch (error: any) {
      console.error('OTP verification error:', error);
      setError('Invalid verification code. Please try again.');
      setLoading(false);
    }
  };

  const completeProfile = async () => {
    if (!name.trim() || !username.trim()) {
      setError('Please fill in all fields');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/create-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: name.trim(),
          username: username.trim(),
          phoneNumber: countryCode + phoneNumber,
          userToken: 'temp-token'
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Store user data in localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('boguani_user', JSON.stringify(data.user));
        }

        setLoading(false);
        setSuccess(true);

        setTimeout(() => {
          router.push('/chat');
        }, 1500);
      } else {
        setError(data.error || 'Failed to create profile');
        setLoading(false);
      }
    } catch (error) {
      setError('Failed to create profile. Please try again.');
      setLoading(false);
    }
  };

  const goBack = () => {
    if (step === 'otp') {
      setStep('phone');
    } else if (step === 'profile') {
      setStep('otp');
    }
    setError('');
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem('boguani_user');
      if (userData) {
        router.push('/chat');
      }
    }
  }, [router]);

  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

        body {
          background: linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%);
          font-family: 'Montserrat', sans-serif;
        }

        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .gold-border {
          border: 2px solid transparent;
          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,
                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;
        }

        .feature-card {
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
        }

        .btn-hover:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
        }

        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .auth-card {
          background: linear-gradient(135deg, rgba(17, 24, 39, 0.8) 0%, rgba(31, 41, 55, 0.8) 100%);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(75, 85, 99, 0.3);
        }

        .auth-input {
          background: rgba(17, 24, 39, 0.8);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(75, 85, 99, 0.5);
          transition: all 0.3s ease;
        }

        .auth-input:focus {
          border-color: #D4AF37;
          box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
          background: rgba(17, 24, 39, 0.9);
        }

        .professional-shadow {
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2);
        }
      `}</style>

      <div className="text-white min-h-screen hero-pattern bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        {/* reCAPTCHA container - invisible */}
        <div id="recaptcha-container"></div>

        <div className="min-h-screen flex items-center justify-center px-6">
          <div className="w-full max-w-md">
            {/* Header */}
            <div className="text-center mb-12">
              <Link href="/" className="inline-block">
                <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center shadow-2xl hover:scale-105 transition-transform duration-300">
                  <i className="fas fa-comment-dollar text-3xl text-purple-900"></i>
                </div>
              </Link>
              <h1 className="text-4xl md:text-5xl font-bold mb-4 gold-gradient">
                Welcome to BoGuani
              </h1>
              <p className="text-xl text-gray-300 mb-2">Messenger of Value</p>
              <p className="text-gray-400 italic">&quot;Speak Gold. Share Value.&quot;</p>
            </div>

            {/* Auth Card */}
            <div className="auth-card p-8 rounded-2xl professional-shadow">
              
              {success ? (
                /* Success State */
                <div className="text-center py-8">
                  <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                    <i className="fas fa-check text-2xl text-purple-900"></i>
                  </div>
                  <h2 className="text-2xl font-bold mb-4 gold-gradient">Welcome to BoGuani!</h2>
                  <p className="text-gray-300 mb-6">Your account has been created successfully.</p>
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto"></div>
                  <p className="text-sm text-gray-400 mt-4">Redirecting to chat...</p>
                </div>
              ) : (
                <>
                  {/* Step Indicator */}
                  <div className="flex justify-center mb-8">
                    <div className="flex space-x-4">
                      <div className={`w-3 h-3 rounded-full ${step === 'phone' ? 'bg-yellow-400' : 'bg-gray-600'}`}></div>
                      <div className={`w-3 h-3 rounded-full ${step === 'otp' ? 'bg-yellow-400' : 'bg-gray-600'}`}></div>
                      <div className={`w-3 h-3 rounded-full ${step === 'profile' ? 'bg-yellow-400' : 'bg-gray-600'}`}></div>
                    </div>
                  </div>

                  {/* Error Message */}
                  {error && (
                    <div className="bg-red-900 bg-opacity-50 border border-red-500 rounded-lg p-4 mb-6">
                      <div className="flex items-center">
                        <i className="fas fa-exclamation-triangle text-red-400 mr-3"></i>
                        <p className="text-red-200 text-sm">{error}</p>
                      </div>
                    </div>
                  )}

                  {/* Phone Step */}
                  {step === 'phone' && (
                    <>
                      <div className="mb-6">
                        <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">Enter Your Phone</h2>
                        <p className="text-gray-400 text-sm">We&apos;ll send you a verification code</p>
                      </div>

                      <form onSubmit={(e) => { e.preventDefault(); sendOTP(); }} className="space-y-6">
                        <div>
                          <label htmlFor="countryCode" className="block text-gray-300 text-sm font-medium mb-2">Country</label>
                          <select
                            id="countryCode"
                            value={countryCode}
                            onChange={(e) => setCountryCode(e.target.value)}
                            className="w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400"
                          >
                            {countryCodes.map((country, index) => (
                              <option key={index} value={country.code}>
                                {country.flag} {country.code} ({country.country})
                              </option>
                            ))}
                          </select>
                        </div>

                        <div>
                          <label htmlFor="phone" className="block text-gray-300 text-sm font-medium mb-2">Phone Number</label>
                          <div className="flex space-x-2">
                            <div className="w-20 px-3 py-3 auth-input rounded-lg text-center text-gray-300">
                              {countryCode}
                            </div>
                            <input
                              type="tel"
                              id="phone"
                              value={phoneNumber}
                              onChange={(e) => setPhoneNumber(e.target.value)}
                              placeholder="1234567890"
                              required
                              className="flex-1 px-4 py-3 auth-input rounded-lg text-white placeholder-gray-400 focus:outline-none"
                            />
                          </div>
                        </div>

                        <button
                          type="submit"
                          disabled={loading}
                          className="w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {loading ? (
                            <>
                              <i className="fas fa-spinner fa-spin mr-2"></i>
                              Sending Code...
                            </>
                          ) : (
                            <>
                              <i className="fas fa-paper-plane mr-2"></i>
                              Send Verification Code
                            </>
                          )}
                        </button>
                      </form>
                    </>
                  )}

                  {/* OTP Step */}
                  {step === 'otp' && (
                    <>
                      <div className="mb-6">
                        <button onClick={goBack} className="text-yellow-400 hover:text-yellow-200 transition-colors mb-4">
                          <i className="fas fa-arrow-left mr-2"></i>
                          Back
                        </button>
                        <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">Enter Verification Code</h2>
                        <p className="text-gray-400 text-sm">We sent a 6-digit code to {countryCode}{phoneNumber}</p>
                      </div>

                      <form onSubmit={(e) => { e.preventDefault(); verifyOTP(); }} className="space-y-6">
                        <div>
                          <label htmlFor="otp" className="block text-gray-300 text-sm font-medium mb-2">Verification Code</label>
                          <input
                            type="text"
                            id="otp"
                            value={otpCode}
                            onChange={(e) => setOtpCode(e.target.value)}
                            placeholder="123456"
                            maxLength={6}
                            required
                            className="w-full px-4 py-3 auth-input rounded-lg text-white text-center text-2xl tracking-widest placeholder-gray-400 focus:outline-none"
                          />
                        </div>

                        <button
                          type="submit"
                          disabled={loading}
                          className="w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {loading ? (
                            <>
                              <i className="fas fa-spinner fa-spin mr-2"></i>
                              Verifying...
                            </>
                          ) : (
                            <>
                              <i className="fas fa-check mr-2"></i>
                              Verify Code
                            </>
                          )}
                        </button>
                      </form>
                    </>
                  )}

                  {/* Profile Step */}
                  {step === 'profile' && (
                    <>
                      <div className="mb-6">
                        <button onClick={goBack} className="text-yellow-400 hover:text-yellow-200 transition-colors mb-4">
                          <i className="fas fa-arrow-left mr-2"></i>
                          Back
                        </button>
                        <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">Create Your Profile</h2>
                        <p className="text-gray-400 text-sm">Tell us a bit about yourself</p>
                      </div>

                      <form onSubmit={(e) => { e.preventDefault(); completeProfile(); }} className="space-y-6">
                        <div>
                          <label htmlFor="name" className="block text-gray-300 text-sm font-medium mb-2">Full Name</label>
                          <input
                            type="text"
                            id="name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            placeholder="John Doe"
                            required
                            className="w-full px-4 py-3 auth-input rounded-lg text-white placeholder-gray-400 focus:outline-none"
                          />
                        </div>

                        <div>
                          <label htmlFor="username" className="block text-gray-300 text-sm font-medium mb-2">Username</label>
                          <input
                            type="text"
                            id="username"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            placeholder="johndoe"
                            required
                            className="w-full px-4 py-3 auth-input rounded-lg text-white placeholder-gray-400 focus:outline-none"
                          />
                        </div>

                        <button
                          type="submit"
                          disabled={loading}
                          className="w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {loading ? (
                            <>
                              <i className="fas fa-spinner fa-spin mr-2"></i>
                              Creating Profile...
                            </>
                          ) : (
                            <>
                              <i className="fas fa-user-plus mr-2"></i>
                              Complete Setup
                            </>
                          )}
                        </button>
                      </form>
                    </>
                  )}
                </>
              )}
            </div>

            {/* Footer */}
            <div className="text-center mt-8">
              <p className="text-gray-400 text-sm">
                By continuing, you agree to our{' '}
                <Link href="/terms" className="text-yellow-400 hover:text-yellow-200 transition-colors">Terms</Link> and{' '}
                <Link href="/privacy" className="text-yellow-400 hover:text-yellow-200 transition-colors">Privacy Policy</Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
