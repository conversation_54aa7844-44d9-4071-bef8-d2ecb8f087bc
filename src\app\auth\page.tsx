'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { auth } from '@/lib/firebase';
import { RecaptchaVerifier, signInWithPhoneNumber, ConfirmationResult } from 'firebase/auth';
import Layout from '@/components/Layout';
import { debugFirebaseConfig, getFirebaseErrorHelp } from '@/utils/firebase-debug';

// Extend window type for reCAPTCHA
declare global {
  interface Window {
    recaptchaVerifier: RecaptchaVerifier;
  }
}

interface CountryCode {
  code: string;
  country: string;
  flag: string;
}

export default function AuthPage() {
  const router = useRouter();
  const [step, setStep] = useState<'phone' | 'otp' | 'profile'>('phone');
  const [countryCode, setCountryCode] = useState('+1');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otpCode, setOtpCode] = useState('');
  const [name, setName] = useState('');
  const [username, setUsername] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [confirmationResult, setConfirmationResult] = useState<ConfirmationResult | null>(null);
  const [recaptchaVerifier, setRecaptchaVerifier] = useState<RecaptchaVerifier | null>(null);

  const countryCodes: CountryCode[] = [
    { code: '+1', country: 'United States', flag: '🇺🇸' },
    { code: '+1', country: 'Canada', flag: '🇨🇦' },
    { code: '+44', country: 'United Kingdom', flag: '🇬🇧' },
    { code: '+33', country: 'France', flag: '🇫🇷' },
    { code: '+49', country: 'Germany', flag: '🇩🇪' },
    { code: '+81', country: 'Japan', flag: '🇯🇵' },
    { code: '+86', country: 'China', flag: '🇨🇳' },
    { code: '+91', country: 'India', flag: '🇮🇳' },
    { code: '+55', country: 'Brazil', flag: '🇧🇷' },
    { code: '+61', country: 'Australia', flag: '🇦🇺' }
  ];

  // Initialize reCAPTCHA properly for Firebase Auth
  useEffect(() => {
    // Clean up any existing reCAPTCHA
    if (typeof window !== 'undefined') {
      const existingRecaptcha = document.getElementById('recaptcha-container');
      if (existingRecaptcha) {
        existingRecaptcha.innerHTML = '';
      }

      // Log Firebase configuration for debugging
      debugFirebaseConfig();
    }
  }, []);

  const sendOTP = async () => {
    if (!phoneNumber.trim()) {
      setError('Please enter your phone number');
      return;
    }

    setLoading(true);
    setError('');

    const fullPhoneNumber = countryCode + phoneNumber;
    console.log('Sending SMS via Firebase Auth to:', fullPhoneNumber);

    try {
      // Clear any existing reCAPTCHA
      const recaptchaContainer = document.getElementById('recaptcha-container');
      if (recaptchaContainer) {
        recaptchaContainer.innerHTML = '';
      }

      // Wait a moment for cleanup
      await new Promise(resolve => setTimeout(resolve, 500));

      // Create reCAPTCHA verifier
      console.log('Creating reCAPTCHA verifier...');

      // Use window.recaptchaVerifier to avoid multiple instances
      if (window.recaptchaVerifier) {
        window.recaptchaVerifier.clear();
      }

      window.recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
        size: 'invisible',
        callback: (response: any) => {
          console.log('reCAPTCHA solved:', response);
        }
      });

      // Send SMS via Firebase Auth
      console.log('Attempting to send SMS...');
      const confirmationResult = await signInWithPhoneNumber(auth, fullPhoneNumber, window.recaptchaVerifier);

      console.log('Firebase SMS sent successfully!');
      setConfirmationResult(confirmationResult);
      setStep('otp');
      setLoading(false);
      setError('SMS sent! Check your phone for the verification code.');

    } catch (error: any) {
      console.error('Firebase Auth SMS error:', error);
      console.error('Error code:', error.code);
      console.error('Error message:', error.message);
      console.error('Full error object:', error);

      // Handle billing-related errors specifically
      let errorMessage = '';

      if (error.code === 'auth/billing-not-enabled' || error.message.includes('billing')) {
        errorMessage = `Billing Error: Your Firebase project has Blaze plan (ID: 0175FB-F6213F-0AB866) but Firebase isn't recognizing it.

🔧 Quick fixes:
1. Wait 5-10 minutes for billing to propagate
2. Try using test phone number: +1 ************ with code: 123456
3. Check Firebase Console billing status
4. Contact Firebase support if issue persists`;
      } else {
        const errorHelp = getFirebaseErrorHelp(error.code);
        errorMessage = `${error.code}: ${error.message}\n\n${errorHelp}`;
      }

      setError(errorMessage);
      setLoading(false);

      // Clean up reCAPTCHA on error
      if (window.recaptchaVerifier) {
        window.recaptchaVerifier.clear();
      }
      const recaptchaContainer = document.getElementById('recaptcha-container');
      if (recaptchaContainer) {
        recaptchaContainer.innerHTML = '';
      }
    }
  };

  const verifyOTP = async () => {
    if (!otpCode.trim() || otpCode.length !== 6) {
      setError('Please enter a valid 6-digit code');
      return;
    }

    if (!confirmationResult) {
      setError('No verification session found. Please request a new code.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      console.log('Verifying OTP with Firebase Auth...');
      const result = await confirmationResult.confirm(otpCode);
      console.log('Firebase phone verification successful!', result.user.uid);

      // Store Firebase user info for profile creation
      const firebaseUser = result.user;

      setStep('profile');
      setLoading(false);

    } catch (error: any) {
      console.error('Firebase OTP verification error:', error);

      let errorMessage = '';
      switch (error.code) {
        case 'auth/invalid-verification-code':
          errorMessage = 'Invalid verification code. Please check and try again.';
          break;
        case 'auth/code-expired':
          errorMessage = 'Verification code has expired. Please request a new code.';
          break;
        default:
          errorMessage = 'Invalid verification code. Please try again.';
      }

      setError(errorMessage);
      setLoading(false);
    }
  };

  const completeProfile = async () => {
    if (!name.trim() || !username.trim()) {
      setError('Please fill in all fields');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/create-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: name.trim(),
          username: username.trim(),
          phoneNumber: countryCode + phoneNumber,
          userToken: 'temp-token'
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Store user data in localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('boguani_user', JSON.stringify(data.user));
        }

        setLoading(false);
        setSuccess(true);

        setTimeout(() => {
          router.push('/chat');
        }, 1500);
      } else {
        setError(data.error || 'Failed to create profile');
        setLoading(false);
      }
    } catch (error) {
      setError('Failed to create profile. Please try again.');
      setLoading(false);
    }
  };

  const goBack = () => {
    if (step === 'otp') {
      setStep('phone');
    } else if (step === 'profile') {
      setStep('otp');
    }
    setError('');
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem('boguani_user');
      if (userData) {
        router.push('/chat');
      }
    }
  }, [router]);

  return (
    <Layout showNavLinks={false} showFooter={false}>
      <style jsx global>{`
        .auth-card {
          background: linear-gradient(135deg, rgba(17, 24, 39, 0.8) 0%, rgba(31, 41, 55, 0.8) 100%);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(75, 85, 99, 0.3);
        }

        .auth-input {
          background: rgba(17, 24, 39, 0.8);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(75, 85, 99, 0.5);
          transition: all 0.3s ease;
        }

        .auth-input:focus {
          border-color: #D4AF37;
          box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
          background: rgba(17, 24, 39, 0.9);
        }
      `}</style>
        {/* reCAPTCHA container for Firebase Auth */}
        <div id="recaptcha-container"></div>

        <div className="min-h-screen flex items-center justify-center px-6">
          <div className="w-full max-w-md">
            {/* Header */}
            <div className="text-center mb-12">
              <Link href="/" className="inline-block">
                <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center shadow-2xl hover:scale-105 transition-transform duration-300">
                  <i className="fas fa-comment-dollar text-3xl text-purple-900"></i>
                </div>
              </Link>
              <h1 className="text-4xl md:text-5xl font-bold mb-4 gold-gradient">
                Welcome to BoGuani
              </h1>
              <p className="text-xl text-gray-300 mb-2">Messenger of Value</p>
              <p className="text-gray-400 italic">&quot;Speak Gold. Share Value.&quot;</p>
            </div>

            {/* Auth Card */}
            <div className="auth-card p-8 rounded-2xl professional-shadow">
              
              {success ? (
                /* Success State */
                <div className="text-center py-8">
                  <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                    <i className="fas fa-check text-2xl text-purple-900"></i>
                  </div>
                  <h2 className="text-2xl font-bold mb-4 gold-gradient">Welcome to BoGuani!</h2>
                  <p className="text-gray-300 mb-6">Your account has been created successfully.</p>
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto"></div>
                  <p className="text-sm text-gray-400 mt-4">Redirecting to chat...</p>
                </div>
              ) : (
                <>
                  {/* Step Indicator */}
                  <div className="flex justify-center mb-8">
                    <div className="flex space-x-4">
                      <div className={`w-3 h-3 rounded-full ${step === 'phone' ? 'bg-yellow-400' : 'bg-gray-600'}`}></div>
                      <div className={`w-3 h-3 rounded-full ${step === 'otp' ? 'bg-yellow-400' : 'bg-gray-600'}`}></div>
                      <div className={`w-3 h-3 rounded-full ${step === 'profile' ? 'bg-yellow-400' : 'bg-gray-600'}`}></div>
                    </div>
                  </div>

                  {/* Error Message */}
                  {error && (
                    <div className="bg-red-900 bg-opacity-50 border border-red-500 rounded-lg p-4 mb-6">
                      <div className="flex items-center">
                        <i className="fas fa-exclamation-triangle text-red-400 mr-3"></i>
                        <p className="text-red-200 text-sm">{error}</p>
                      </div>
                    </div>
                  )}

                  {/* Phone Step */}
                  {step === 'phone' && (
                    <>
                      <div className="mb-6">
                        <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">Enter Your Phone</h2>
                        <p className="text-gray-400 text-sm">We&apos;ll send you a verification code</p>
                      </div>

                      <form onSubmit={(e) => { e.preventDefault(); sendOTP(); }} className="space-y-6">
                        <div>
                          <label htmlFor="countryCode" className="block text-gray-300 text-sm font-medium mb-2">Country</label>
                          <select
                            id="countryCode"
                            value={countryCode}
                            onChange={(e) => setCountryCode(e.target.value)}
                            className="w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400"
                          >
                            {countryCodes.map((country, index) => (
                              <option key={index} value={country.code}>
                                {country.flag} {country.code} ({country.country})
                              </option>
                            ))}
                          </select>
                        </div>

                        <div>
                          <label htmlFor="phone" className="block text-gray-300 text-sm font-medium mb-2">Phone Number</label>
                          <div className="flex space-x-2">
                            <div className="w-20 px-3 py-3 auth-input rounded-lg text-center text-gray-300">
                              {countryCode}
                            </div>
                            <input
                              type="tel"
                              id="phone"
                              value={phoneNumber}
                              onChange={(e) => setPhoneNumber(e.target.value)}
                              placeholder="1234567890 (or ************ for test)"
                              required
                              className="flex-1 px-4 py-3 auth-input rounded-lg text-white placeholder-gray-400 focus:outline-none"
                            />
                          </div>
                        </div>

                        <button
                          type="submit"
                          disabled={loading}
                          className="w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {loading ? (
                            <>
                              <i className="fas fa-spinner fa-spin mr-2"></i>
                              Sending Code...
                            </>
                          ) : (
                            <>
                              <i className="fas fa-paper-plane mr-2"></i>
                              Send Verification Code
                            </>
                          )}
                        </button>
                      </form>
                    </>
                  )}

                  {/* OTP Step */}
                  {step === 'otp' && (
                    <>
                      <div className="mb-6">
                        <button onClick={goBack} className="text-yellow-400 hover:text-yellow-200 transition-colors mb-4">
                          <i className="fas fa-arrow-left mr-2"></i>
                          Back
                        </button>
                        <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">Enter Verification Code</h2>
                        <p className="text-gray-400 text-sm">We sent a 6-digit code to {countryCode}{phoneNumber}</p>
                      </div>

                      <form onSubmit={(e) => { e.preventDefault(); verifyOTP(); }} className="space-y-6">
                        <div>
                          <label htmlFor="otp" className="block text-gray-300 text-sm font-medium mb-2">Verification Code</label>
                          <input
                            type="text"
                            id="otp"
                            value={otpCode}
                            onChange={(e) => setOtpCode(e.target.value)}
                            placeholder="123456"
                            maxLength={6}
                            required
                            className="w-full px-4 py-3 auth-input rounded-lg text-white text-center text-2xl tracking-widest placeholder-gray-400 focus:outline-none"
                          />
                        </div>

                        <button
                          type="submit"
                          disabled={loading}
                          className="w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {loading ? (
                            <>
                              <i className="fas fa-spinner fa-spin mr-2"></i>
                              Verifying...
                            </>
                          ) : (
                            <>
                              <i className="fas fa-check mr-2"></i>
                              Verify Code
                            </>
                          )}
                        </button>
                      </form>
                    </>
                  )}

                  {/* Profile Step */}
                  {step === 'profile' && (
                    <>
                      <div className="mb-6">
                        <button onClick={goBack} className="text-yellow-400 hover:text-yellow-200 transition-colors mb-4">
                          <i className="fas fa-arrow-left mr-2"></i>
                          Back
                        </button>
                        <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">Create Your Profile</h2>
                        <p className="text-gray-400 text-sm">Tell us a bit about yourself</p>
                      </div>

                      <form onSubmit={(e) => { e.preventDefault(); completeProfile(); }} className="space-y-6">
                        <div>
                          <label htmlFor="name" className="block text-gray-300 text-sm font-medium mb-2">Full Name</label>
                          <input
                            type="text"
                            id="name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            placeholder="John Doe"
                            required
                            className="w-full px-4 py-3 auth-input rounded-lg text-white placeholder-gray-400 focus:outline-none"
                          />
                        </div>

                        <div>
                          <label htmlFor="username" className="block text-gray-300 text-sm font-medium mb-2">Username</label>
                          <input
                            type="text"
                            id="username"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            placeholder="johndoe"
                            required
                            className="w-full px-4 py-3 auth-input rounded-lg text-white placeholder-gray-400 focus:outline-none"
                          />
                        </div>

                        <button
                          type="submit"
                          disabled={loading}
                          className="w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {loading ? (
                            <>
                              <i className="fas fa-spinner fa-spin mr-2"></i>
                              Creating Profile...
                            </>
                          ) : (
                            <>
                              <i className="fas fa-user-plus mr-2"></i>
                              Complete Setup
                            </>
                          )}
                        </button>
                      </form>
                    </>
                  )}
                </>
              )}
            </div>

            {/* Footer */}
            <div className="text-center mt-8">
              <p className="text-gray-400 text-sm">
                By continuing, you agree to our{' '}
                <Link href="/terms" className="text-yellow-400 hover:text-yellow-200 transition-colors">Terms</Link> and{' '}
                <Link href="/privacy" className="text-yellow-400 hover:text-yellow-200 transition-colors">Privacy Policy</Link>
              </p>
            </div>
          </div>
        </div>
    </Layout>
  );
}
