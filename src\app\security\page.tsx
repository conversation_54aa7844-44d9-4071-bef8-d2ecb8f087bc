'use client';

import { motion } from 'framer-motion';
import Layout from '@/components/Layout';

export default function SecurityPage() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Security & Trust</h1>
            <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Your security is our top priority. Learn about the advanced measures we take to protect your messages, payments, and personal information.
            </p>
          </div>
        </div>
      </section>

      {/* Security Features */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-t border-gray-600/30">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Security Features</h2>
            <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Advanced protection for your communications and transactions
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-lock text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">End-to-End Encryption</h3>
                <p className="text-gray-300 leading-relaxed">
                  All messages and payments are encrypted on your device using military-grade AES-256 encryption.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-key text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Zero-Knowledge Architecture</h3>
                <p className="text-gray-300 leading-relaxed">
                  We cannot access your message content. Your private keys never leave your device.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-shield-virus text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Advanced Threat Protection</h3>
                <p className="text-gray-300 leading-relaxed">
                  Real-time monitoring and AI-powered threat detection protect against security threats.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-user-shield text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Identity Verification</h3>
                <p className="text-gray-300 leading-relaxed">
                  Multi-factor authentication and biometric verification ensure only you can access your account.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-server text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Secure Infrastructure</h3>
                <p className="text-gray-300 leading-relaxed">
                  Enterprise-grade servers with 99.9% uptime, distributed across multiple secure data centers.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-certificate text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Compliance & Audits</h3>
                <p className="text-gray-300 leading-relaxed">
                  Regular security audits and compliance with international standards including PCI DSS and GDPR.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </Layout>
  );
}