import { NextRequest, NextResponse } from 'next/server';
import { rateLimit } from '@/lib/rate-limit';
import admin from 'firebase-admin';

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
  });
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = await rateLimit(request);
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      );
    }

    const { phoneNumber } = await request.json();

    // Validate phone number
    if (!phoneNumber || typeof phoneNumber !== 'string') {
      return NextResponse.json(
        { error: 'Phone number is required' },
        { status: 400 }
      );
    }

    // Validate phone number format (basic validation)
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    if (!phoneRegex.test(phoneNumber.replace(/\s+/g, ''))) {
      return NextResponse.json(
        { error: 'Invalid phone number format' },
        { status: 400 }
      );
    }

    try {
      // Create a custom token for the phone number
      // This will be used to verify the phone number with Firebase Auth
      const customToken = await admin.auth().createCustomToken(`phone_${phoneNumber.replace(/\D/g, '')}`);

      // Store the phone number temporarily for verification
      // In production, you might want to use Redis or a database
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // For now, we'll use Firebase Auth client-side verification
      // The client will handle the actual SMS sending
      return NextResponse.json({
        success: true,
        message: 'Ready for verification. Please use Firebase Auth on client side.',
        sessionId,
        customToken,
        phoneNumber
      });

    } catch (authError: any) {
      console.error('Firebase Auth error:', authError);
      return NextResponse.json(
        { error: 'Failed to initialize phone verification' },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error('Send OTP error:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to send verification code' },
      { status: 500 }
    );
  }
}
