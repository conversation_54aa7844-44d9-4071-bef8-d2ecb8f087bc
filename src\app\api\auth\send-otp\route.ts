import { NextRequest, NextResponse } from 'next/server';
import { rateLimit } from '@/lib/rate-limit';

// Simple in-memory OTP storage (use Redis in production)
declare global {
  var otpStorage: Map<string, { code: string; expires: number }> | undefined;
}

const otpStorage = globalThis.otpStorage || new Map<string, { code: string; expires: number }>();
globalThis.otpStorage = otpStorage;

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = await rateLimit(request);
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      );
    }

    const { phoneNumber } = await request.json();

    // Validate phone number
    if (!phoneNumber || typeof phoneNumber !== 'string') {
      return NextResponse.json(
        { error: 'Phone number is required' },
        { status: 400 }
      );
    }

    // Validate phone number format (basic validation)
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    if (!phoneRegex.test(phoneNumber.replace(/\s+/g, ''))) {
      return NextResponse.json(
        { error: 'Invalid phone number format' },
        { status: 400 }
      );
    }

    // Generate a 6-digit OTP
    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Store OTP with 5-minute expiration
    otpStorage.set(phoneNumber, {
      code: otpCode,
      expires: Date.now() + 5 * 60 * 1000 // 5 minutes
    });

    try {
      // Try to send SMS using Twilio if configured
      if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
        const twilio = require('twilio')(
          process.env.TWILIO_ACCOUNT_SID,
          process.env.TWILIO_AUTH_TOKEN
        );

        await twilio.messages.create({
          body: `Your BoGuani verification code is: ${otpCode}`,
          from: process.env.TWILIO_PHONE_NUMBER,
          to: phoneNumber
        });

        console.log(`SMS sent to ${phoneNumber} with code: ${otpCode}`);
      } else {
        // For development - log the OTP to console
        console.log(`🔐 OTP for ${phoneNumber}: ${otpCode}`);
        console.log('📱 SMS would be sent in production with Twilio');
      }

      return NextResponse.json({
        success: true,
        message: 'Verification code sent successfully',
        sessionId,
        // In development, include the OTP for testing
        ...(process.env.NODE_ENV === 'development' && {
          developmentOTP: otpCode,
          note: 'OTP included for development testing only'
        })
      });

    } catch (smsError: any) {
      console.error('SMS sending error:', smsError);

      // Still return success but log the OTP for development
      console.log(`🔐 OTP for ${phoneNumber}: ${otpCode} (SMS failed)`);

      return NextResponse.json({
        success: true,
        message: 'Verification code generated (check console for development)',
        sessionId,
        ...(process.env.NODE_ENV === 'development' && {
          developmentOTP: otpCode,
          note: 'SMS service unavailable - using development mode'
        })
      });
    }

  } catch (error: any) {
    console.error('Send OTP error:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to send verification code' },
      { status: 500 }
    );
  }
}
