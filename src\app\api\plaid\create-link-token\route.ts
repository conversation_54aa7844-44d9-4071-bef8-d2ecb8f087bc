import { NextRequest, NextResponse } from 'next/server';
import { plaidService } from '@/lib/plaid';
import { rateLimit } from '@/lib/rate-limit';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = await rateLimit(request);
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      );
    }

    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const linkToken = await plaidService.createLinkToken(userId);

    return NextResponse.json({
      success: true,
      linkToken
    });

  } catch (error: any) {
    console.error('Create link token error:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to create link token' },
      { status: 500 }
    );
  }
}
