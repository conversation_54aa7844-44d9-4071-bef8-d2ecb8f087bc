# Firebase Configuration Setup for BoGuani

## 🔥 Firebase Console Configuration Checklist

### 1. **Enable Phone Authentication**
1. Go to [Firebase Console - Authentication](https://console.firebase.google.com/project/chatpay-4922e/authentication/providers)
2. Click on "Phone" provider
3. Enable the Phone provider
4. Save changes

### 2. **Configure Authorized Domains**
1. Go to [Firebase Console - Authentication Settings](https://console.firebase.google.com/project/chatpay-4922e/authentication/settings)
2. Scroll down to "Authorized domains"
3. Add these domains:
   - `localhost`
   - `127.0.0.1`
   - `chatpay-4922e.firebaseapp.com` (your Firebase hosting domain)
   - Your production domain (if any)

### 3. **Enable Billing (Required for SMS)**
1. Go to [Firebase Console - Usage and Billing](https://console.firebase.google.com/project/chatpay-4922e/usage)
2. Upgrade to Blaze plan (pay-as-you-go)
3. This is required for SMS authentication

### 4. **Configure reCAPTCHA**
1. Go to [Firebase Console - Authentication Settings](https://console.firebase.google.com/project/chatpay-4922e/authentication/settings)
2. Scroll to "reCAPTCHA Enforcement"
3. Ensure it's enabled for phone authentication

### 5. **Test Configuration**
1. Visit: http://localhost:3000/firebase-test
2. Run all tests to verify configuration
3. Check browser console for any errors

## 🛠️ Local Development Setup

### Environment Variables (Already Configured)
```env
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyBqMCY_vblZIApBW5I6aShR1iVQIUtnXJ0
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=chatpay-4922e.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=chatpay-4922e
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=chatpay-4922e.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=211600276697
NEXT_PUBLIC_FIREBASE_APP_ID=1:211600276697:web:a94ed4b6baaf7a654492c8
```

### Firebase CLI Commands
```bash
# Login to Firebase (already done)
npx firebase login

# Set project
npx firebase use chatpay-4922e

# Deploy Firestore rules (if needed)
npx firebase deploy --only firestore:rules

# Start emulators for testing
npx firebase emulators:start
```

## 🔍 Troubleshooting Common Issues

### Issue 1: "auth/internal-error"
**Solution:**
- Check that phone authentication is enabled
- Verify authorized domains include localhost
- Ensure billing is enabled

### Issue 2: "auth/captcha-check-failed"
**Solution:**
- Clear browser cache
- Disable ad blockers
- Try incognito mode
- Check reCAPTCHA configuration

### Issue 3: "auth/quota-exceeded"
**Solution:**
- Check Firebase usage limits
- Upgrade billing plan if needed
- Wait for quota reset

### Issue 4: "auth/too-many-requests"
**Solution:**
- Wait 15 minutes before trying again
- Use different phone number for testing
- Check rate limiting settings

## 📱 Testing Phone Authentication

### Test Numbers (Firebase Test Mode)
If you enable test mode in Firebase Console:
- `******-555-3434` with code `123456`
- `******-555-3435` with code `654321`

### Real Phone Testing
1. Use your real phone number
2. Check SMS delivery
3. Verify OTP code works

## 🚀 Quick Fix Commands

### Clear Browser Data
```javascript
// Run in browser console
localStorage.clear();
sessionStorage.clear();
location.reload();
```

### Reset Firebase Auth
```javascript
// Run in browser console
firebase.auth().signOut();
```

## 📞 Support Links

- [Firebase Phone Auth Documentation](https://firebase.google.com/docs/auth/web/phone-auth)
- [Firebase Console](https://console.firebase.google.com/project/chatpay-4922e)
- [Firebase Status Page](https://status.firebase.google.com/)

## ✅ Final Checklist

- [ ] Phone authentication enabled
- [ ] Authorized domains configured
- [ ] Billing enabled (Blaze plan)
- [ ] reCAPTCHA configured
- [ ] Test page working: http://localhost:3000/firebase-test
- [ ] Auth page working: http://localhost:3000/auth
- [ ] Browser console shows no errors
- [ ] SMS delivery working

## 🎯 Next Steps

1. Complete the checklist above
2. Test authentication with your phone number
3. If issues persist, check the Firebase test page for specific errors
4. Contact Firebase support if needed

---

**Project:** BoGuani Chat Application  
**Firebase Project:** chatpay-4922e  
**Environment:** Development  
**Last Updated:** $(date)
