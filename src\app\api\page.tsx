'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';

export default function APIPage() {
  return (
    <div className="min-h-screen text-white" style={{
      background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)',
      fontFamily: 'Montserrat, sans-serif'
    }}>
      <div className="relative min-h-screen" style={{
        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)',
        backgroundImage: 'radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)'
      }}>
        {/* Navigation */}
        <nav className="fixed w-full z-50 bg-gradient-to-r from-black/80 to-purple-900/80 backdrop-blur-md border-b border-yellow-500/20">
          <div className="container mx-auto px-6 py-4 flex justify-between items-center">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <div className="text-yellow-500 text-3xl mr-3">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                  BoGuani
                </span>
              </Link>
            </div>
            <div className="flex space-x-6">
              <Link href="/" className="hover:text-yellow-400 transition-colors">Home</Link>
              <Link href="/guides" className="hover:text-yellow-400 transition-colors">Guides</Link>
              <Link href="/support" className="hover:text-yellow-400 transition-colors">Support</Link>
              <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-4 py-2 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all">
                Open Web App
              </Link>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <div className="pt-24 pb-16 px-6">
          <div className="container mx-auto max-w-6xl">
            {/* Header */}
            <div className="text-center mb-16">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                <i className="fas fa-code text-3xl text-purple-900"></i>
              </div>
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                API Documentation
              </h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Integrate BoGuani&apos;s secure messaging and payment features into your applications with our powerful API.
              </p>
            </div>

            {/* Quick Start */}
            <div className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 mb-12">
              <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">Quick Start</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-4 bg-yellow-400 rounded-full flex items-center justify-center">
                    <span className="text-purple-900 font-bold">1</span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-yellow-200">Get API Key</h3>
                  <p className="text-gray-300 text-sm">Register your application and get your API credentials</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-4 bg-yellow-400 rounded-full flex items-center justify-center">
                    <span className="text-purple-900 font-bold">2</span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-yellow-200">Authenticate</h3>
                  <p className="text-gray-300 text-sm">Use OAuth 2.0 or API key authentication</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-4 bg-yellow-400 rounded-full flex items-center justify-center">
                    <span className="text-purple-900 font-bold">3</span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-yellow-200">Start Building</h3>
                  <p className="text-gray-300 text-sm">Send messages and process payments programmatically</p>
                </div>
              </div>
            </div>

            {/* API Endpoints */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              {/* Messaging API */}
              <motion.div
                className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20"
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
              >
                <div className="flex items-center mb-4">
                  <i className="fas fa-comments text-yellow-400 text-2xl mr-3"></i>
                  <h3 className="text-2xl font-bold text-yellow-200">Messaging API</h3>
                </div>
                <p className="text-gray-300 mb-4">Send and receive encrypted messages programmatically</p>
                <div className="space-y-3">
                  <div className="bg-purple-900/50 p-3 rounded-lg">
                    <code className="text-green-400">POST /api/v1/messages</code>
                    <p className="text-gray-400 text-sm mt-1">Send a message</p>
                  </div>
                  <div className="bg-purple-900/50 p-3 rounded-lg">
                    <code className="text-blue-400">GET /api/v1/messages</code>
                    <p className="text-gray-400 text-sm mt-1">Retrieve messages</p>
                  </div>
                  <div className="bg-purple-900/50 p-3 rounded-lg">
                    <code className="text-yellow-400">PUT /api/v1/messages/:id</code>
                    <p className="text-gray-400 text-sm mt-1">Update message status</p>
                  </div>
                </div>
              </motion.div>

              {/* Payments API */}
              <motion.div
                className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                <div className="flex items-center mb-4">
                  <i className="fas fa-credit-card text-yellow-400 text-2xl mr-3"></i>
                  <h3 className="text-2xl font-bold text-yellow-200">Payments API</h3>
                </div>
                <p className="text-gray-300 mb-4">Process secure payments and transfers</p>
                <div className="space-y-3">
                  <div className="bg-purple-900/50 p-3 rounded-lg">
                    <code className="text-green-400">POST /api/v1/payments</code>
                    <p className="text-gray-400 text-sm mt-1">Create payment</p>
                  </div>
                  <div className="bg-purple-900/50 p-3 rounded-lg">
                    <code className="text-blue-400">GET /api/v1/payments/:id</code>
                    <p className="text-gray-400 text-sm mt-1">Get payment status</p>
                  </div>
                  <div className="bg-purple-900/50 p-3 rounded-lg">
                    <code className="text-purple-400">POST /api/v1/payments/:id/refund</code>
                    <p className="text-gray-400 text-sm mt-1">Refund payment</p>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Code Examples */}
            <div className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 mb-12">
              <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">Code Examples</h2>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* JavaScript Example */}
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-yellow-200">JavaScript</h3>
                  <div className="bg-black p-4 rounded-lg overflow-x-auto">
                    <pre className="text-sm">
                      <code className="text-gray-300">
{`const boguani = require('boguani-sdk');

const client = new boguani.Client({
  apiKey: 'your-api-key'
});

// Send a message
const message = await client.messages.send({
  to: '+1234567890',
  text: 'Hello from BoGuani!'
});`}
                      </code>
                    </pre>
                  </div>
                </div>

                {/* Python Example */}
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-yellow-200">Python</h3>
                  <div className="bg-black p-4 rounded-lg overflow-x-auto">
                    <pre className="text-sm">
                      <code className="text-gray-300">
{`import boguani

client = boguani.Client(
    api_key='your-api-key'
)

# Send a payment
payment = client.payments.create({
    'amount': 100.00,
    'currency': 'USD',
    'recipient': '+1234567890'
})`}
                      </code>
                    </pre>
                  </div>
                </div>
              </div>
            </div>

            {/* SDKs and Libraries */}
            <div className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 mb-12">
              <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">SDKs & Libraries</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {[
                  { name: 'JavaScript', icon: 'fab fa-js-square', install: 'npm install boguani-sdk' },
                  { name: 'Python', icon: 'fab fa-python', install: 'pip install boguani' },
                  { name: 'PHP', icon: 'fab fa-php', install: 'composer require boguani/sdk' },
                  { name: 'Java', icon: 'fab fa-java', install: 'Maven & Gradle' }
                ].map((sdk, index) => (
                  <div key={index} className="text-center">
                    <i className={`${sdk.icon} text-4xl text-yellow-400 mb-3`}></i>
                    <h3 className="font-semibold text-yellow-200">{sdk.name}</h3>
                    <p className="text-gray-400 text-sm">{sdk.install}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Get Started */}
            <div className="text-center">
              <div className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 max-w-2xl mx-auto">
                <h3 className="text-2xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">Ready to Build?</h3>
                <p className="text-gray-300 mb-6">Get your API credentials and start integrating BoGuani today.</p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all">
                    <i className="fas fa-key mr-2"></i>
                    Get API Key
                  </Link>
                  <Link href="/guides" className="border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all">
                    <i className="fas fa-book mr-2"></i>
                    View Guides
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
