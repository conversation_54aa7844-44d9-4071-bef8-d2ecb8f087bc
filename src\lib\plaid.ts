import { Configuration, PlaidApi, PlaidEnvironments } from 'plaid';

// Plaid configuration
const configuration = new Configuration({
  basePath: process.env.PLAID_ENV === 'production' 
    ? PlaidEnvironments.production 
    : PlaidEnvironments.sandbox,
  baseOptions: {
    headers: {
      'PLAID-CLIENT-ID': process.env.PLAID_CLIENT_ID,
      'PLAID-SECRET': process.env.PLAID_SECRET,
    },
  },
});

export const plaidClient = new PlaidApi(configuration);

export interface BankAccount {
  id: string;
  name: string;
  mask: string;
  type: string;
  subtype: string;
  balance: number;
  isConnected: boolean;
}

export interface Transaction {
  id: string;
  amount: number;
  currency: string;
  description: string;
  date: string;
  status: 'pending' | 'completed' | 'failed';
  fromAccount?: string;
  toAccount?: string;
  recipientPhone?: string;
  senderPhone?: string;
}

class PlaidService {
  // Create link token for Plaid Link
  async createLinkToken(userId: string): Promise<string> {
    try {
      const response = await plaidClient.linkTokenCreate({
        user: {
          client_user_id: userId,
        },
        client_name: 'BoGuani',
        products: ['transactions', 'auth'],
        country_codes: ['US'],
        language: 'en',
        webhook: `${process.env.NEXT_PUBLIC_APP_URL}/api/plaid/webhook`,
        account_filters: {
          depository: {
            account_subtypes: ['checking', 'savings'],
          },
        },
      });

      return response.data.link_token;
    } catch (error) {
      console.error('Error creating link token:', error);
      throw new Error('Failed to create link token');
    }
  }

  // Exchange public token for access token
  async exchangePublicToken(publicToken: string): Promise<string> {
    try {
      const response = await plaidClient.itemPublicTokenExchange({
        public_token: publicToken,
      });

      return response.data.access_token;
    } catch (error) {
      console.error('Error exchanging public token:', error);
      throw new Error('Failed to exchange public token');
    }
  }

  // Get account information
  async getAccounts(accessToken: string): Promise<BankAccount[]> {
    try {
      const response = await plaidClient.accountsGet({
        access_token: accessToken,
      });

      return response.data.accounts.map(account => ({
        id: account.account_id,
        name: account.name,
        mask: account.mask || '',
        type: account.type,
        subtype: account.subtype || '',
        balance: account.balances.current || 0,
        isConnected: true,
      }));
    } catch (error) {
      console.error('Error getting accounts:', error);
      throw new Error('Failed to get accounts');
    }
  }

  // Get account balance
  async getBalance(accessToken: string, accountId: string): Promise<number> {
    try {
      const response = await plaidClient.accountsBalanceGet({
        access_token: accessToken,
        options: {
          account_ids: [accountId],
        },
      });

      const account = response.data.accounts.find(acc => acc.account_id === accountId);
      return account?.balances.current || 0;
    } catch (error) {
      console.error('Error getting balance:', error);
      throw new Error('Failed to get balance');
    }
  }

  // Initiate transfer (simplified for demo)
  async initiateTransfer(
    fromAccessToken: string,
    fromAccountId: string,
    amount: number,
    recipientPhone: string,
    description: string
  ): Promise<Transaction> {
    try {
      // In a real implementation, you would:
      // 1. Verify recipient's bank account
      // 2. Use Plaid's transfer API or ACH
      // 3. Handle compliance and verification
      
      // For demo purposes, create a mock transaction
      const transaction: Transaction = {
        id: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        amount,
        currency: 'USD',
        description,
        date: new Date().toISOString(),
        status: 'pending',
        fromAccount: fromAccountId,
        recipientPhone,
        senderPhone: 'current_user_phone'
      };

      // Simulate processing delay
      setTimeout(() => {
        // Update transaction status to completed
        // In real app, this would be handled by webhooks
      }, 3000);

      return transaction;
    } catch (error) {
      console.error('Error initiating transfer:', error);
      throw new Error('Failed to initiate transfer');
    }
  }

  // Get transaction history
  async getTransactions(accessToken: string, accountId: string): Promise<Transaction[]> {
    try {
      const response = await plaidClient.transactionsGet({
        access_token: accessToken,
        start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end_date: new Date().toISOString().split('T')[0],
        options: {
          account_ids: [accountId],
          count: 50,
        },
      });

      return response.data.transactions.map(txn => ({
        id: txn.transaction_id,
        amount: txn.amount,
        currency: txn.iso_currency_code || 'USD',
        description: txn.name,
        date: txn.date,
        status: 'completed' as const,
      }));
    } catch (error) {
      console.error('Error getting transactions:', error);
      throw new Error('Failed to get transactions');
    }
  }

  // Verify account ownership
  async verifyAccount(accessToken: string, accountId: string): Promise<boolean> {
    try {
      const response = await plaidClient.authGet({
        access_token: accessToken,
      });

      return response.data.accounts.some(account => account.account_id === accountId);
    } catch (error) {
      console.error('Error verifying account:', error);
      return false;
    }
  }
}

export const plaidService = new PlaidService();
export default plaidService;
