'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import Layout from '@/components/Layout';

export default function PricingPage() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Pricing Plans</h1>
            <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Choose the perfect plan for your communication needs. All plans include end-to-end encryption and secure money transfers.
            </p>
          </div>
        </div>
      </section>

      {/* Pricing Plans */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-t border-gray-600/30">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Choose Your Plan</h2>
            <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              All plans include military-grade security and instant transfers
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Free Plan */}
            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-user text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-2xl font-bold mb-4 gold-gradient">Free</h3>
                <div className="text-4xl font-bold mb-6 gold-gradient">$0</div>
                <p className="text-gray-300 mb-8">Perfect for personal use</p>

                <ul className="space-y-4 mb-8 text-left">
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Unlimited messaging</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Voice & video calls</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">End-to-end encryption</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Money transfers up to $100/month</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Group chats (up to 10 people)</span>
                  </li>
                </ul>

                <Link href="/auth" className="w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  Get Started
                </Link>
              </div>
            </motion.div>

            {/* Pro Plan */}
            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow relative"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-4 py-2 rounded-full text-sm font-semibold">
                Most Popular
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-users text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-2xl font-bold mb-4 gold-gradient">Pro</h3>
                <div className="text-4xl font-bold mb-6 gold-gradient">$9.99</div>
                <p className="text-gray-300 mb-8">For power users and small teams</p>

                <ul className="space-y-4 mb-8 text-left">
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Everything in Free</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Money transfers up to $5,000/month</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Group chats (up to 100 people)</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Priority customer support</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Advanced security features</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">File sharing up to 100MB</span>
                  </li>
                </ul>

                <Link href="/auth" className="w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  Start Free Trial
                </Link>
              </div>
            </motion.div>

            {/* Business Plan */}
            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-building text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-2xl font-bold mb-4 gold-gradient">Business</h3>
                <div className="text-4xl font-bold mb-6 gold-gradient">$29.99</div>
                <p className="text-gray-300 mb-8">For businesses and organizations</p>

                <ul className="space-y-4 mb-8 text-left">
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Everything in Pro</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Unlimited money transfers</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Unlimited group size</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Admin controls & analytics</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">24/7 dedicated support</span>
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">API access</span>
                  </li>
                </ul>

                <Link href="/contact" className="w-full border-2 border-yellow-400 text-yellow-400 px-6 py-3 rounded-full font-semibold hover:bg-yellow-400 hover:text-purple-900 transition-all inline-block">
                  Contact Sales
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </Layout>
  );
}

        {/* FAQ Section */}
        <section className="py-20 bg-gray-900">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto"></div>
            </div>
            
            <div className="max-w-4xl mx-auto space-y-6">
              <div className="bg-purple-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-3 text-yellow-400">Can I change my plan anytime?</h3>
                <p className="text-gray-300">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
              </div>
              
              <div className="bg-purple-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-3 text-yellow-400">Are there any hidden fees?</h3>
                <p className="text-gray-300">No hidden fees. The price you see is what you pay. Money transfer fees may apply based on your bank.</p>
              </div>
              
              <div className="bg-purple-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-3 text-yellow-400">Is there a free trial?</h3>
                <p className="text-gray-300">Yes! Pro and Business plans come with a 14-day free trial. No credit card required.</p>
              </div>
              
              <div className="bg-purple-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-3 text-yellow-400">What payment methods do you accept?</h3>
                <p className="text-gray-300">We accept all major credit cards, PayPal, and bank transfers for Business plans.</p>
              </div>
            </div>
          </div>
        </section>


    </Layout>
  );
}
