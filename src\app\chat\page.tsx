'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import SettingsModal from '../../components/SettingsModal';
import { firestoreService } from '@/lib/firestore';
import type { User as FirestoreUser, Chat as FirestoreChat, Message as FirestoreMessage } from '@/lib/firestore';

type MessageType = 'text' | 'payment' | 'image' | 'voice' | 'video' | 'file' | 'location';
type MessageStatus = 'sent' | 'delivered' | 'read' | 'sending' | 'failed';
type CallType = 'voice' | 'video';

interface User {
  id: string;
  name: string;
  username: string;
  phone: string;
  isOnline: boolean;
  lastSeen?: string;
  avatar?: string;
  status?: string;
  isTyping?: boolean;
}

interface Message {
  id: string;
  senderId: string;
  text: string;
  timestamp: Date;
  status: MessageStatus;
  type: MessageType;
  amount?: number;
  currency?: string;
  imageUrl?: string;
  fileUrl?: string;
  fileName?: string;
  fileSize?: string;
  duration?: string;
  location?: { lat: number; lng: number; address: string };
  replyTo?: string;
  isForwarded?: boolean;
  reactions?: { emoji: string; users: string[] }[];
}

interface Chat {
  id: string;
  participant: User;
  lastMessage: string;
  timestamp: Date;
  unreadCount: number;
  messages: Message[];
  isPinned?: boolean;
  isMuted?: boolean;
  isArchived?: boolean;
}

interface CallState {
  isActive: boolean;
  type: CallType;
  participant?: User;
  duration?: string;
  isMuted?: boolean;
  isVideoOff?: boolean;
}

// Mock data for demonstration
const mockUsers: User[] = [
  {
    id: '1',
    name: 'John Doe',
    username: 'johndoe',
    phone: '+1234567890',
    isOnline: true,
    lastSeen: '2 min ago',
    avatar: 'JD',
    status: 'Available for chat'
  },
  {
    id: '2',
    name: 'Jane Smith',
    username: 'janesmith',
    phone: '+1234567891',
    isOnline: false,
    lastSeen: '1 hour ago',
    avatar: 'JS',
    status: 'Busy with work'
  },
  {
    id: '3',
    name: 'Mike Johnson',
    username: 'mikej',
    phone: '+1234567892',
    isOnline: true,
    lastSeen: 'now',
    avatar: 'MJ',
    status: 'Ready to receive payments'
  },
  {
    id: '4',
    name: 'Sarah Wilson',
    username: 'sarahw',
    phone: '+1234567893',
    isOnline: false,
    lastSeen: '30 min ago',
    avatar: 'SW',
    status: 'At the gym'
  },
];

const mockMessages: Message[] = [
  {
    id: '1',
    senderId: '1',
    text: 'Hey! How are you doing?',
    timestamp: new Date(Date.now() - 1000 * 60 * 30),
    status: 'read',
    type: 'text'
  },
  {
    id: '2',
    senderId: 'current',
    text: 'I&apos;m doing great! Just finished a big project.',
    timestamp: new Date(Date.now() - 1000 * 60 * 25),
    status: 'read',
    type: 'text'
  },
  {
    id: '3',
    senderId: '1',
    text: 'That&apos;s awesome! Want to celebrate? I can send you some money for dinner 🍽️',
    timestamp: new Date(Date.now() - 1000 * 60 * 20),
    status: 'read',
    type: 'text'
  },
  {
    id: '4',
    senderId: '1',
    text: 'Here you go!',
    timestamp: new Date(Date.now() - 1000 * 60 * 15),
    status: 'read',
    type: 'payment',
    amount: 50,
    currency: 'USD'
  },
  {
    id: '5',
    senderId: 'current',
    text: 'Thank you so much! 🙏',
    timestamp: new Date(Date.now() - 1000 * 60 * 10),
    status: 'read',
    type: 'text'
  },
  {
    id: '6',
    senderId: '1',
    text: 'Check out this photo from my vacation!',
    timestamp: new Date(Date.now() - 1000 * 60 * 8),
    status: 'read',
    type: 'image',
    imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400'
  },
  {
    id: '7',
    senderId: 'current',
    text: 'Beautiful! Where is this?',
    timestamp: new Date(Date.now() - 1000 * 60 * 5),
    status: 'read',
    type: 'text'
  },
  {
    id: '8',
    senderId: '1',
    text: 'Voice message',
    timestamp: new Date(Date.now() - 1000 * 60 * 3),
    status: 'read',
    type: 'voice',
    duration: '0:45'
  },
  {
    id: '9',
    senderId: 'current',
    text: 'Let&apos;s have a video call later!',
    timestamp: new Date(Date.now() - 1000 * 60 * 1),
    status: 'delivered',
    type: 'text'
  }
];

const mockChats: Chat[] = [
  {
    id: '1',
    participant: mockUsers[0],
    lastMessage: 'Can you send me the files?',
    timestamp: new Date(),
    unreadCount: 2,
    messages: mockMessages,
  },
  {
    id: '2',
    participant: mockUsers[1],
    lastMessage: 'Meeting at 3 PM',
    timestamp: new Date(Date.now() - 86400000),
    unreadCount: 0,
    messages: [],
  },
];

export default function ChatPage() {
  const router = useRouter();
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [currentChat, setCurrentChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [chats, setChats] = useState<Chat[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const [callState, setCallState] = useState<CallState>({ isActive: false, type: 'voice' });
  const [showSettings, setShowSettings] = useState(false);
  const [replyToMessage, setReplyToMessage] = useState<Message | null>(null);

  // Enhanced security and banking states
  const [bankBalance] = useState<number>(2847.32);
  const [connectedBank] = useState<string>('Chase ****1234');
  const [isVideoCallActive, setIsVideoCallActive] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    // Load real user data from localStorage
    const loadData = async () => {
      try {
        setIsLoading(true);

        if (typeof window !== 'undefined') {
          const userData = localStorage.getItem('boguani_user');
          if (!userData) {
            router.push('/auth');
            return;
          }

          const user = JSON.parse(userData);
          setCurrentUser(user);

          // Try to load real chats from Firestore, fallback to mock data
          try {
            const userChats = await firestoreService.getUserChats(user.uid);
            if (userChats.length > 0) {
              setChats(userChats);
              setCurrentChat(userChats[0]);
              // Load messages for first chat
              const chatMessages = await firestoreService.getMessages(userChats[0].id);
              setMessages(chatMessages);
            } else {
              // No real chats yet, use mock data
              setChats(mockChats);
              setCurrentChat(mockChats[0]);
              setMessages(mockMessages);
            }
          } catch (firestoreError) {
            console.log('Using mock data as fallback');
            setChats(mockChats);
            setCurrentChat(mockChats[0]);
            setMessages(mockMessages);
          }
        }
      } catch (error) {
        console.error('Error loading chat data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [router]);

  // Auto-scroll to bottom of messages and mark messages as read
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
    
    // Mark messages as read when chat is opened
    if (currentChat) {
      const updatedChats = chats.map(chat => {
        if (chat.id === currentChat.id && chat.unreadCount > 0) {
          return { ...chat, unreadCount: 0 };
        }
        return chat;
      });
      setChats(updatedChats);
    }
  }, [messages, currentChat, chats]);

  const handleSendMessage = (e?: React.FormEvent) => {
    e?.preventDefault();
    if (!newMessage.trim() || !currentChat || !currentUser) return;

    const message: Message = {
      id: Date.now().toString(),
      senderId: currentUser.id,
      text: newMessage,
      timestamp: new Date(),
      status: 'sending',
      type: 'text',
    };

    // Optimistic update
    const updatedMessages = [...messages, message];
    setMessages(updatedMessages);
    setNewMessage('');

    // Simulate message sending
    setTimeout(() => {
      setMessages(prevMessages => 
        prevMessages.map(msg => 
          msg.id === message.id 
            ? { ...msg, status: 'delivered' } 
            : msg
        )
      );
    }, 1000);

    // Update chat list
    const updatedChats = chats.map((chat) =>
      chat.id === currentChat.id
        ? {
            ...chat,
            lastMessage: newMessage,
            timestamp: new Date(),
            unreadCount: 0,
            messages: [...updatedMessages],
          }
        : chat
    );
    
    setChats(updatedChats);
    setCurrentChat(updatedChats.find(chat => chat.id === currentChat.id) || null);
  };

  const handleSendPayment = () => {
    if (!paymentAmount || !currentChat || !currentUser) return;

    const message: Message = {
      id: Date.now().toString(),
      senderId: currentUser.id,
      text: `Payment of $${paymentAmount}`,
      timestamp: new Date(),
      status: 'sent',
      type: 'payment',
      amount: parseFloat(paymentAmount),
      currency: 'USD',
    };

    const updatedMessages = [...messages, message];
    setMessages(updatedMessages);
    setShowPaymentModal(false);
    setPaymentAmount('');

    // Update last message in chats
    const updatedChats = chats.map((chat) =>
      chat.id === currentChat.id
        ? {
            ...chat,
            lastMessage: `Payment of $${paymentAmount}`,
            timestamp: new Date(),
            unreadCount: 0,
            messages: updatedMessages,
          }
        : chat
    );
    setChats(updatedChats);
    setCurrentChat(updatedChats.find(chat => chat.id === currentChat.id) || null);
  };

  const handleLogout = () => {
    // Clear user session and redirect to auth page
    setCurrentUser(null);
    setChats([]);
    setMessages([]);
    setCurrentChat(null);

    // Clear any stored session data
    if (typeof window !== 'undefined') {
      localStorage.removeItem('boguani_user');
      localStorage.removeItem('boguani_session');
    }

    // Redirect to auth page
    router.push('/auth');
  };

  if (isLoading || !currentUser) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-900 to-indigo-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-white">Loading chat...</p>
        </div>
      </div>
    );
  }

  // Format time for messages
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Format date for message grouping
  const formatDate = (date: Date) => {
    return date.toLocaleDateString([], { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  // Get message status icon
  const getStatusIcon = (status: MessageStatus) => {
    switch (status) {
      case 'sending':
        return <span className="text-gray-400">🕒</span>;
      case 'sent':
        return <span className="text-gray-400">✓</span>;
      case 'delivered':
        return <span className="text-gray-400">✓✓</span>;
      case 'read':
        return <span className="text-blue-500">✓✓</span>;
      default:
        return null;
    }
  };

  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

        body {
          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);
          font-family: 'Montserrat', sans-serif;
        }

        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .gold-border {
          border: 2px solid transparent;
          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,
                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;
        }

        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .message-bubble {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(212, 175, 55, 0.2);
        }

        .message-bubble:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
          border-color: rgba(212, 175, 55, 0.4);
        }

        .message-bubble-sent {
          background: rgba(212, 175, 55, 0.15);
          border-color: rgba(212, 175, 55, 0.3);
        }

        .message-bubble-received {
          background: rgba(45, 27, 78, 0.4);
          border-color: rgba(107, 114, 128, 0.3);
        }

        .chat-input {
          background: rgba(17, 24, 39, 0.8);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(75, 85, 99, 0.5);
          transition: all 0.3s ease;
        }

        .chat-input:focus {
          border-color: #D4AF37;
          box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
          background: rgba(17, 24, 39, 0.9);
        }

        .sidebar-panel {
          background: linear-gradient(180deg, rgba(17, 24, 39, 0.95) 0%, rgba(31, 41, 55, 0.95) 100%);
          backdrop-filter: blur(20px);
          border-right: 1px solid rgba(75, 85, 99, 0.3);
        }

        .chat-panel {
          background: linear-gradient(180deg, rgba(17, 24, 39, 0.8) 0%, rgba(31, 41, 55, 0.8) 100%);
          backdrop-filter: blur(20px);
        }

        .encryption-indicator {
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }

        .bank-balance {
          background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(21, 128, 61, 0.1) 100%);
          border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .professional-shadow {
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2);
        }
      `}</style>

      <div className="flex h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 text-white hero-pattern">
        {/* Enhanced Sidebar */}
        <div className="w-full md:w-1/3 sidebar-panel flex flex-col professional-shadow">
          {/* Enhanced Header with Encryption Status */}
          <div className="p-4 border-b border-gray-600/30 flex justify-between items-center sticky top-0 z-10 bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md">
            <div className="flex items-center">
              <div className="relative">
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-purple-900 font-bold text-lg professional-shadow">
                  {currentUser.name.split(' ').map((n: string) => n[0]).join('')}
                </div>
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-gray-900 encryption-indicator" title="End-to-End Encrypted"></div>
              </div>
              <div className="ml-3">
                <h2 className="font-semibold text-white">{currentUser.name}</h2>
                <div className="flex items-center space-x-2">
                  <p className="text-xs text-gray-400">@{currentUser.username}</p>
                  <div className="flex items-center text-xs text-green-400">
                    <i className="fas fa-shield-alt mr-1"></i>
                    <span>E2E Encrypted</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowSettings(true)}
                className="text-gray-400 hover:text-yellow-400 p-2 rounded-full hover:bg-gray-700/50 transition-all duration-300"
                title="Settings"
              >
                <i className="fas fa-cog text-lg"></i>
              </button>
              <button
                onClick={handleLogout}
                className="text-gray-400 hover:text-red-400 p-2 rounded-full hover:bg-gray-700/50 transition-all duration-300"
                title="Logout"
              >
                <i className="fas fa-sign-out-alt text-lg"></i>
              </button>
            </div>
          </div>

          {/* Enhanced Search Bar */}
          <div className="p-4 border-b border-gray-600/30">
            <div className="relative">
              <input
                type="text"
                placeholder="Search conversations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-3 pl-10 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"
              />
              <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"></i>
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white transition-colors"
                >
                  <i className="fas fa-times"></i>
                </button>
              )}
            </div>
          </div>

          {/* Chat List */}
          <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent">
            {chats.map((chat) => (
              <motion.div
                key={chat.id}
                className={`p-4 border-b border-gray-700/30 cursor-pointer transition-all duration-300 hover:bg-gray-800/50 ${
                  currentChat?.id === chat.id ? 'bg-gray-800/70 border-l-4 border-l-yellow-400' : ''
                }`}
                onClick={() => {
                  setCurrentChat(chat);
                  setMessages(chat.messages);
                }}
                whileHover={{ x: 6 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              >
                <div className="flex items-center">
                  <div className="relative">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-white font-semibold professional-shadow">
                      {chat.participant.avatar || chat.participant.name.split(' ').map((n: string) => n[0]).join('')}
                    </div>
                    {chat.participant.isOnline && (
                      <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-900"></div>
                    )}
                    {chat.unreadCount > 0 && (
                      <div className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 text-gray-900 rounded-full flex items-center justify-center text-xs font-bold">
                        {chat.unreadCount}
                      </div>
                    )}
                  </div>
                  <div className="ml-3 flex-1">
                    <div className="flex justify-between items-start">
                      <h3 className="font-semibold text-white">{chat.participant.name}</h3>
                      <span className="text-xs text-gray-500">{formatTime(chat.timestamp)}</span>
                    </div>
                    <p className="text-sm text-gray-400 truncate">{chat.lastMessage}</p>
                    {chat.participant.status && (
                      <p className="text-xs text-yellow-400 italic mt-1">{chat.participant.status}</p>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Bank Balance Section */}
          <div className="p-4 border-t border-gray-600/30 bg-gradient-to-r from-gray-900/95 to-gray-800/95 backdrop-blur-md">
            <div className="bank-balance rounded-xl p-4 backdrop-blur-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <i className="fas fa-university text-green-400 mr-2"></i>
                  <span className="text-sm font-medium text-gray-300">{connectedBank}</span>
                </div>
                <button
                  onClick={() => setShowSettings(true)}
                  className="text-gray-500 hover:text-yellow-400 transition-colors"
                  title="Manage Bank Account"
                >
                  <i className="fas fa-cog text-sm"></i>
                </button>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-500">Available Balance</p>
                  <p className="text-xl font-bold text-green-400">${bankBalance.toLocaleString('en-US', { minimumFractionDigits: 2 })}</p>
                </div>
                <button
                  onClick={() => setShowPaymentModal(true)}
                  className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-4 py-2 rounded-lg font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow"
                >
                  <i className="fas fa-paper-plane mr-2"></i>
                  Send
                </button>
              </div>
              <div className="mt-2 flex items-center text-xs text-gray-500">
                <i className="fas fa-shield-alt mr-1 text-green-400"></i>
                <span>Secured by Plaid & 256-bit AES encryption</span>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Main Chat Area */}
        <div className="flex-1 flex flex-col chat-panel">
          {currentChat ? (
            <>
              {/* Enhanced Chat Header */}
              <div className="p-4 border-b border-gray-600/30 flex items-center justify-between bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md sticky top-0 z-10 professional-shadow">
                <div className="flex items-center">
                  <div className="relative">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-white font-semibold professional-shadow">
                      {currentChat.participant.avatar || currentChat.participant.name.split(' ').map((n: string) => n[0]).join('')}
                    </div>
                    {currentChat.participant.isOnline && (
                      <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-900"></div>
                    )}
                  </div>
                  <div className="ml-3">
                    <h2 className="font-semibold text-white">{currentChat.participant.name}</h2>
                    <div className="flex items-center space-x-2">
                      <p className="text-xs text-gray-400">
                        {currentChat.participant.isOnline
                          ? (currentChat.participant.isTyping ? 'Typing...' : 'Online')
                          : `Last seen ${currentChat.participant.lastSeen}`}
                      </p>
                      <div className="flex items-center text-xs text-green-400">
                        <i className="fas fa-lock mr-1"></i>
                        <span>Encrypted</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Chat Actions */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setCallState({ isActive: true, type: 'voice', participant: currentChat.participant })}
                    className="text-gray-400 hover:text-green-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300"
                    title="Voice Call"
                  >
                    <i className="fas fa-phone text-lg"></i>
                  </button>
                  <button
                    onClick={() => {
                      setCallState({ isActive: true, type: 'video', participant: currentChat.participant });
                      setIsVideoCallActive(true);
                    }}
                    className="text-gray-400 hover:text-blue-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300"
                    title="Video Call"
                  >
                    <i className="fas fa-video text-lg"></i>
                  </button>
                  <button
                    onClick={() => setShowSettings(true)}
                    className="text-gray-400 hover:text-yellow-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300"
                    title="User Info"
                  >
                    <i className="fas fa-info-circle text-lg"></i>
                  </button>
                </div>
              </div>

              {/* Messages Area */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.length > 0 && (
                  <div className="text-center mb-4">
                    <span className="text-xs text-gray-400 bg-purple-800/50 px-3 py-1 rounded-full">
                      {formatDate(messages[0].timestamp)}
                    </span>
                  </div>
                )}

                <div className="space-y-4">
                  {messages.map((message, index) => {
                    const isCurrentUser = message.senderId === currentUser.id;
                    const showDate = index === 0 ||
                      new Date(message.timestamp).toDateString() !==
                      new Date(messages[index - 1].timestamp).toDateString();

                    return (
                      <motion.div
                        key={message.id}
                        className="space-y-1"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        {showDate && index !== 0 && (
                          <div className="text-center my-4">
                            <span className="text-xs text-gray-400 bg-purple-800/50 px-3 py-1 rounded-full">
                              {formatDate(message.timestamp)}
                            </span>
                          </div>
                        )}

                        <div className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>
                          <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl message-bubble ${
                            isCurrentUser
                              ? 'message-bubble-sent text-white rounded-tr-none'
                              : 'message-bubble-received text-white rounded-tl-none'
                          }`}>

                            {/* Enhanced Payment Message */}
                            {message.type === 'payment' && (
                              <div className="flex items-center mb-2 p-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl border border-green-400/30 backdrop-blur-lg">
                                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                                  <i className="fas fa-dollar-sign text-white text-sm"></i>
                                </div>
                                <div>
                                  <span className="text-sm font-semibold text-green-400">
                                    Payment Sent: ${message.amount?.toFixed(2)} {message.currency}
                                  </span>
                                  <p className="text-xs text-gray-400 mt-1">Secured by Plaid • Instant Transfer</p>
                                </div>
                              </div>
                            )}

                            {/* Image Message */}
                            {message.type === 'image' && message.imageUrl && (
                              <div className="mb-2">
                                <Image
                                  src={message.imageUrl}
                                  alt="Shared image"
                                  width={300}
                                  height={200}
                                  className="rounded-lg max-w-full h-auto cursor-pointer hover:opacity-90 transition-opacity"
                                  onClick={() => window.open(message.imageUrl, '_blank')}
                                />
                              </div>
                            )}

                            {/* Enhanced Voice Message */}
                            {message.type === 'voice' && (
                              <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-gray-700/40 to-gray-800/40 rounded-xl border border-gray-600/30 backdrop-blur-lg">
                                <button className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 rounded-full flex items-center justify-center hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow">
                                  <i className="fas fa-play text-sm"></i>
                                </button>
                                <div className="flex-1 h-2 bg-gray-600/50 rounded-full">
                                  <div className="h-full w-1/3 bg-gradient-to-r from-yellow-400 to-yellow-200 rounded-full"></div>
                                </div>
                                <span className="text-xs text-gray-400 font-medium">{message.duration}</span>
                              </div>
                            )}

                            {/* Enhanced Text Message */}
                            {(message.type === 'text' || message.type === 'payment') && (
                              <p className={`${message.type === 'payment' ? 'text-sm' : ''} leading-relaxed`}>
                                {message.text}
                              </p>
                            )}

                            {/* Enhanced Message Footer */}
                            <div className="flex items-center justify-end mt-2 space-x-2">
                              <span className={`text-xs ${isCurrentUser ? 'text-gray-400' : 'text-gray-500'} font-medium`}>
                                {formatTime(message.timestamp)}
                              </span>
                              {isCurrentUser && (
                                <span className="ml-1">
                                  {getStatusIcon(message.status)}
                                </span>
                              )}
                              <div className="flex items-center text-xs text-green-400">
                                <i className="fas fa-lock text-xs"></i>
                              </div>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
                <div ref={messagesEndRef} />
              </div>

              {/* Enhanced Message Input */}
              <div className="p-4 border-t border-gray-600/30 bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md professional-shadow">
                {/* Reply Preview */}
                {replyToMessage && (
                  <div className="mb-3 p-3 bg-purple-800/50 rounded-lg border-l-4 border-yellow-400">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-xs text-yellow-400 font-semibold">Replying to {replyToMessage.senderId === currentUser.id ? 'yourself' : currentChat.participant.name}</p>
                        <p className="text-sm text-gray-300 truncate">{replyToMessage.text}</p>
                      </div>
                      <button
                        onClick={() => setReplyToMessage(null)}
                        className="text-gray-400 hover:text-white"
                      >
                        <i className="fas fa-times"></i>
                      </button>
                    </div>
                  </div>
                )}

                {/* Attachment Menu */}
                <AnimatePresence>
                  {showAttachmentMenu && (
                    <motion.div
                      className="mb-3 flex space-x-2"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                    >
                      <button className="flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors">
                        <i className="fas fa-image text-yellow-400"></i>
                        <span className="text-sm text-white">Photo</span>
                      </button>
                      <button className="flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors">
                        <i className="fas fa-file text-yellow-400"></i>
                        <span className="text-sm text-white">Document</span>
                      </button>
                      <button className="flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors">
                        <i className="fas fa-map-marker-alt text-yellow-400"></i>
                        <span className="text-sm text-white">Location</span>
                      </button>
                    </motion.div>
                  )}
                </AnimatePresence>

                <form onSubmit={handleSendMessage} className="flex items-end space-x-3">
                  {/* Enhanced Attachment Button */}
                  <button
                    type="button"
                    onClick={() => setShowAttachmentMenu(!showAttachmentMenu)}
                    className="p-3 text-gray-400 hover:text-yellow-400 hover:bg-gray-700/50 rounded-full transition-all duration-300"
                  >
                    <i className="fas fa-plus text-lg"></i>
                  </button>

                  {/* Message Input */}
                  <div className="flex-1 relative">
                    <textarea
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSendMessage(e);
                        }
                      }}
                      placeholder="Type a message..."
                      rows={1}
                      className="w-full py-3 px-4 pr-20 chat-input rounded-2xl text-white placeholder-gray-500 focus:outline-none resize-none"
                      style={{ minHeight: '48px', maxHeight: '120px' }}
                    />

                    {/* Enhanced Input Actions */}
                    <div className="absolute right-3 bottom-3 flex items-center space-x-2">
                      <button
                        type="button"
                        onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                        className="text-gray-500 hover:text-yellow-400 transition-all duration-300"
                      >
                        <i className="fas fa-smile text-lg"></i>
                      </button>
                      <button
                        type="button"
                        className="text-gray-500 hover:text-blue-400 transition-all duration-300"
                        title="Voice Message"
                      >
                        <i className="fas fa-microphone text-lg"></i>
                      </button>
                    </div>
                  </div>

                  {/* Enhanced Send/Payment Buttons */}
                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={() => setShowPaymentModal(true)}
                      className="p-3 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-white rounded-full transition-all duration-300 professional-shadow"
                      title="Send Money via Plaid"
                    >
                      <i className="fas fa-dollar-sign text-lg"></i>
                    </button>

                    <button
                      type="submit"
                      disabled={!newMessage.trim()}
                      className={`p-3 rounded-full transition-all duration-300 professional-shadow ${
                        newMessage.trim()
                          ? 'bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 hover:from-yellow-300 hover:to-yellow-100'
                          : 'bg-gray-700 text-gray-500 cursor-not-allowed'
                      }`}
                    >
                      <i className="fas fa-paper-plane text-lg"></i>
                    </button>
                  </div>
                </form>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center p-8">
                <div className="w-20 h-20 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mx-auto mb-6">
                  <i className="fas fa-comment-dollar text-3xl text-purple-900"></i>
                </div>
                <h3 className="text-xl font-medium text-white mb-2">Welcome to BoGuani</h3>
                <p className="text-gray-300">Select a conversation to start messaging</p>
                <p className="text-yellow-400 text-sm mt-2 italic">&quot;Speak Gold. Share Value.&quot;</p>
              </div>
            </div>
          )}
        </div>

        {/* Enhanced Call Interface */}
        <AnimatePresence>
          {callState.isActive && (
            <motion.div
              className="fixed inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black z-50 flex items-center justify-center backdrop-blur-lg"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <div className="text-center text-white">
                {/* Video Call Area */}
                {callState.type === 'video' && isVideoCallActive && (
                  <div className="mb-8">
                    <div className="relative w-80 h-60 bg-gray-800 rounded-2xl overflow-hidden professional-shadow">
                      <video
                        ref={remoteVideoRef}
                        autoPlay
                        playsInline
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute bottom-4 right-4 w-20 h-16 bg-gray-700 rounded-lg overflow-hidden">
                        <video
                          ref={localVideoRef}
                          autoPlay
                          playsInline
                          muted
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Call Info */}
                <div className="w-32 h-32 bg-gradient-to-br from-gray-600 to-gray-700 rounded-full flex items-center justify-center mx-auto mb-6 professional-shadow">
                  <span className="text-3xl font-bold">
                    {callState.participant?.avatar || callState.participant?.name.split(' ').map((n: string) => n[0]).join('')}
                  </span>
                </div>
                <h2 className="text-3xl font-semibold mb-2">{callState.participant?.name}</h2>
                <div className="flex items-center justify-center mb-2">
                  <i className="fas fa-shield-alt text-green-400 mr-2"></i>
                  <p className="text-green-400 text-sm">End-to-End Encrypted</p>
                </div>
                <p className="text-gray-400 mb-8 text-lg">
                  {callState.type === 'video' ? 'Video calling...' : 'Voice calling...'}
                </p>

                {/* Enhanced Call Controls */}
                <div className="flex justify-center space-x-6">
                  <button
                    onClick={() => setCallState(prev => ({ ...prev, isMuted: !prev.isMuted }))}
                    className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 professional-shadow ${
                      callState.isMuted ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-700 hover:bg-gray-600'
                    }`}
                  >
                    <i className={`fas ${callState.isMuted ? 'fa-microphone-slash' : 'fa-microphone'} text-xl`}></i>
                  </button>

                  {callState.type === 'video' && (
                    <button
                      onClick={() => setCallState(prev => ({ ...prev, isVideoOff: !prev.isVideoOff }))}
                      className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 professional-shadow ${
                        callState.isVideoOff ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-700 hover:bg-gray-600'
                      }`}
                    >
                      <i className={`fas ${callState.isVideoOff ? 'fa-video-slash' : 'fa-video'} text-xl`}></i>
                    </button>
                  )}

                  <button
                    onClick={() => {
                      setCallState({ isActive: false, type: 'voice' });
                      setIsVideoCallActive(false);
                    }}
                    className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center hover:bg-red-700 transition-all duration-300 professional-shadow"
                  >
                    <i className="fas fa-phone-slash text-xl"></i>
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Settings Modal */}
      <SettingsModal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        currentUser={currentUser}
        bankBalance={bankBalance}
        connectedBank={connectedBank}
      />

      {/* Payment Modal */}
      {showPaymentModal && (
        <motion.div
          className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-lg rounded-2xl shadow-2xl w-full max-w-md border border-gray-600/50 professional-shadow"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h3 className="text-xl font-semibold text-white gold-gradient">Send Payment</h3>
                  <div className="flex items-center mt-1">
                    <i className="fas fa-shield-alt text-green-400 mr-2 text-sm"></i>
                    <span className="text-xs text-green-400">Secured by Plaid</span>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setShowPaymentModal(false);
                    setPaymentAmount('');
                  }}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <i className="fas fa-times text-xl"></i>
                </button>
              </div>

              {/* Bank Account Info */}
              <div className="mb-4 p-3 bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl border border-gray-600/30">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <i className="fas fa-university text-green-400 mr-2"></i>
                    <span className="text-sm text-gray-300">{connectedBank}</span>
                  </div>
                  <span className="text-sm text-green-400 font-semibold">${bankBalance.toLocaleString()}</span>
                </div>
              </div>

              <div className="mb-6">
                <label htmlFor="amount" className="block text-sm font-medium text-gray-300 mb-2">
                  Amount (USD)
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <span className="text-yellow-400 text-lg font-semibold">$</span>
                  </div>
                  <input
                    type="number"
                    name="amount"
                    id="amount"
                    value={paymentAmount}
                    onChange={(e) => setPaymentAmount(e.target.value)}
                    className="w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none text-lg"
                    placeholder="0.00"
                    step="0.01"
                    min="0.01"
                  />
                </div>
                <div className="flex justify-between items-center mt-2">
                  <p className="text-xs text-gray-500">
                    Sending to: {currentChat?.participant.name}
                  </p>
                  <p className="text-xs text-green-400">
                    <i className="fas fa-bolt mr-1"></i>
                    Instant Transfer
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowPaymentModal(false);
                    setPaymentAmount('');
                  }}
                  className="px-6 py-3 bg-gray-700 text-white rounded-xl hover:bg-gray-600 transition-all duration-300"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSendPayment}
                  disabled={!paymentAmount || parseFloat(paymentAmount) <= 0}
                  className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 professional-shadow ${
                    paymentAmount && parseFloat(paymentAmount) > 0
                      ? 'bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 hover:from-yellow-300 hover:to-yellow-100'
                      : 'bg-gray-700 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  <i className="fas fa-paper-plane mr-2"></i>
                  Send ${paymentAmount || '0.00'}
                </button>
              </div>

              {/* Security Notice */}
              <div className="mt-4 p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-400/20">
                <div className="flex items-center text-xs text-green-400">
                  <i className="fas fa-lock mr-2"></i>
                  <span>256-bit AES encryption • FDIC insured • Instant settlement</span>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </>
  );
}
