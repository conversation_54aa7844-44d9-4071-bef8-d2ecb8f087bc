'use client';

import Link from 'next/link';

export default function BlogPage() {
  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');
        
        body {
          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);
          font-family: 'Montserrat', sans-serif;
        }
        
        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }
        
        .gold-border {
          border: 2px solid transparent;
          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,
                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;
        }
        
        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
      `}</style>

      <div className="text-white min-h-screen hero-pattern">
        {/* Navigation */}
        <nav className="bg-gray-900 bg-opacity-80 backdrop-blur-md fixed w-full z-10">
          <div className="container mx-auto px-6 py-3 flex justify-between items-center">
            <Link href="/" className="flex items-center">
              <div className="text-yellow-400 text-3xl mr-2">
                <i className="fas fa-comment-dollar"></i>
              </div>
              <span className="font-bold text-2xl gold-gradient">BoGuani</span>
            </Link>
            <div className="hidden md:flex space-x-8">
              <Link href="/#features" className="hover:text-yellow-400 transition-colors">Features</Link>
              <Link href="/#about" className="hover:text-yellow-400 transition-colors">About</Link>
              <Link href="/#download" className="hover:text-yellow-400 transition-colors">Download</Link>
              <Link href="/support" className="hover:text-yellow-400 transition-colors">Support</Link>
            </div>
          </div>
        </nav>

        {/* Hero Section */}
        <section className="pt-24 pb-16">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h1 className="text-5xl lg:text-6xl font-bold mb-6 gold-gradient">BoGuani Blog</h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Insights, updates, and stories from the future of value-based communication.
              </p>
            </div>
          </div>
        </section>

        {/* Featured Article */}
        <section className="py-20 bg-purple-900">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <div className="bg-purple-800 rounded-2xl overflow-hidden">
                <div className="h-64 bg-gradient-to-r from-yellow-400 to-yellow-500"></div>
                <div className="p-8">
                  <div className="flex items-center mb-4">
                    <span className="bg-yellow-400 text-gray-900 px-3 py-1 rounded-full text-sm font-semibold mr-4">Featured</span>
                    <span className="text-gray-400 text-sm">December 15, 2024</span>
                  </div>
                  <h2 className="text-3xl font-bold mb-4 text-yellow-400">The Future of Value-Based Communication</h2>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    Discover how BoGuani is revolutionizing the way we think about messaging by combining ancient wisdom with modern technology. Learn about our journey from concept to the secure, value-driven platform that&apos;s changing how people connect and share worth.
                  </p>
                  <Link href="/blog/future-of-value-communication" className="bg-yellow-400 text-gray-900 px-6 py-3 rounded-full font-semibold hover:bg-yellow-300 transition-colors inline-block">
                    Read More
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Blog Posts Grid */}
        <section className="py-20 bg-gray-900">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">Latest Articles</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto"></div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <article className="bg-purple-800 rounded-xl overflow-hidden hover:transform hover:scale-105 transition-all duration-300">
                <div className="h-48 bg-gradient-to-br from-purple-600 to-purple-700"></div>
                <div className="p-6">
                  <div className="flex items-center mb-3">
                    <span className="bg-purple-600 text-white px-2 py-1 rounded text-xs font-semibold mr-3">Security</span>
                    <span className="text-gray-400 text-sm">Dec 10, 2024</span>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-yellow-400">End-to-End Encryption Explained</h3>
                  <p className="text-gray-300 text-sm mb-4">Understanding how BoGuani protects your messages with military-grade encryption.</p>
                  <Link href="/blog/encryption-explained" className="text-yellow-400 hover:text-yellow-300 text-sm font-semibold">Read More →</Link>
                </div>
              </article>

              <article className="bg-purple-800 rounded-xl overflow-hidden hover:transform hover:scale-105 transition-all duration-300">
                <div className="h-48 bg-gradient-to-br from-yellow-400 to-yellow-500"></div>
                <div className="p-6">
                  <div className="flex items-center mb-3">
                    <span className="bg-yellow-600 text-white px-2 py-1 rounded text-xs font-semibold mr-3">Product</span>
                    <span className="text-gray-400 text-sm">Dec 8, 2024</span>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-yellow-400">Introducing Voice & Video Calls</h3>
                  <p className="text-gray-300 text-sm mb-4">Crystal-clear communication with the same security standards as our messaging.</p>
                  <Link href="/blog/voice-video-calls" className="text-yellow-400 hover:text-yellow-300 text-sm font-semibold">Read More →</Link>
                </div>
              </article>

              <article className="bg-purple-800 rounded-xl overflow-hidden hover:transform hover:scale-105 transition-all duration-300">
                <div className="h-48 bg-gradient-to-br from-green-500 to-green-600"></div>
                <div className="p-6">
                  <div className="flex items-center mb-3">
                    <span className="bg-green-600 text-white px-2 py-1 rounded text-xs font-semibold mr-3">Finance</span>
                    <span className="text-gray-400 text-sm">Dec 5, 2024</span>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-yellow-400">Secure Money Transfers Made Simple</h3>
                  <p className="text-gray-300 text-sm mb-4">How BoGuani makes sending money as easy as sending a message.</p>
                  <Link href="/blog/money-transfers" className="text-yellow-400 hover:text-yellow-300 text-sm font-semibold">Read More →</Link>
                </div>
              </article>

              <article className="bg-purple-800 rounded-xl overflow-hidden hover:transform hover:scale-105 transition-all duration-300">
                <div className="h-48 bg-gradient-to-br from-blue-500 to-blue-600"></div>
                <div className="p-6">
                  <div className="flex items-center mb-3">
                    <span className="bg-blue-600 text-white px-2 py-1 rounded text-xs font-semibold mr-3">Culture</span>
                    <span className="text-gray-400 text-sm">Dec 1, 2024</span>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-yellow-400">Taíno Wisdom in Modern Tech</h3>
                  <p className="text-gray-300 text-sm mb-4">How ancient communication principles inspire our modern platform.</p>
                  <Link href="/blog/taino-wisdom" className="text-yellow-400 hover:text-yellow-300 text-sm font-semibold">Read More →</Link>
                </div>
              </article>

              <article className="bg-purple-800 rounded-xl overflow-hidden hover:transform hover:scale-105 transition-all duration-300">
                <div className="h-48 bg-gradient-to-br from-red-500 to-red-600"></div>
                <div className="p-6">
                  <div className="flex items-center mb-3">
                    <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-semibold mr-3">Company</span>
                    <span className="text-gray-400 text-sm">Nov 28, 2024</span>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-yellow-400">Building a Remote-First Team</h3>
                  <p className="text-gray-300 text-sm mb-4">Our journey to creating a globally distributed team focused on innovation.</p>
                  <Link href="/blog/remote-team" className="text-yellow-400 hover:text-yellow-300 text-sm font-semibold">Read More →</Link>
                </div>
              </article>

              <article className="bg-purple-800 rounded-xl overflow-hidden hover:transform hover:scale-105 transition-all duration-300">
                <div className="h-48 bg-gradient-to-br from-indigo-500 to-indigo-600"></div>
                <div className="p-6">
                  <div className="flex items-center mb-3">
                    <span className="bg-indigo-600 text-white px-2 py-1 rounded text-xs font-semibold mr-3">Tech</span>
                    <span className="text-gray-400 text-sm">Nov 25, 2024</span>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-yellow-400">WebRTC: The Future of Real-Time Communication</h3>
                  <p className="text-gray-300 text-sm mb-4">Deep dive into the technology powering our voice and video calls.</p>
                  <Link href="/blog/webrtc-technology" className="text-yellow-400 hover:text-yellow-300 text-sm font-semibold">Read More →</Link>
                </div>
              </article>
            </div>
          </div>
        </section>

        {/* Newsletter Signup */}
        <section className="py-20 bg-purple-900">
          <div className="container mx-auto px-6">
            <div className="max-w-2xl mx-auto text-center">
              <h2 className="text-3xl font-bold mb-4">Stay Updated</h2>
              <p className="text-gray-300 mb-8">Get the latest insights and updates from BoGuani delivered to your inbox.</p>
              
              <form className="flex flex-col sm:flex-row gap-4">
                <input 
                  type="email" 
                  placeholder="Enter your email"
                  className="flex-1 px-6 py-4 bg-purple-700 rounded-full border border-purple-600 focus:border-yellow-400 focus:outline-none text-white placeholder-gray-400"
                />
                <button 
                  type="submit"
                  className="bg-yellow-400 text-gray-900 px-8 py-4 rounded-full font-semibold hover:bg-yellow-300 transition-colors"
                >
                  Subscribe
                </button>
              </form>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 py-12">
          <div className="container mx-auto px-6">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-6 md:mb-0">
                <Link href="/" className="flex items-center">
                  <div className="text-yellow-400 text-2xl mr-2">
                    <i className="fas fa-comment-dollar"></i>
                  </div>
                  <span className="font-bold text-xl gold-gradient">BoGuani</span>
                </Link>
                <p className="text-gray-400 mt-2">Messenger of Value</p>
              </div>
              
              <div className="text-center">
                <p className="text-gray-400 text-sm">© 2024 BoGuani. All rights reserved.</p>
                <div className="flex space-x-6 text-gray-400 text-sm mt-2">
                  <Link href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy Policy</Link>
                  <Link href="/terms" className="hover:text-yellow-400 transition-colors">Terms of Service</Link>
                  <Link href="/security" className="hover:text-yellow-400 transition-colors">Security</Link>
                </div>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
