// Firebase Debug Utilities
import { auth } from '@/lib/firebase';

export const debugFirebaseConfig = () => {
  console.group('🔥 Firebase Configuration Debug');
  
  // Check basic config
  console.log('Project ID:', auth.app.options.projectId);
  console.log('Auth Domain:', auth.app.options.authDomain);
  console.log('API Key (partial):', auth.app.options.apiKey?.substring(0, 10) + '...');
  
  // Check current user
  console.log('Current User:', auth.currentUser?.uid || 'Not authenticated');
  
  // Check environment
  console.log('Environment:', process.env.NODE_ENV);
  console.log('Current URL:', typeof window !== 'undefined' ? window.location.origin : 'Server-side');
  
  // Check browser compatibility
  if (typeof window !== 'undefined') {
    console.log('User Agent:', navigator.userAgent);
    console.log('Local Storage Available:', !!window.localStorage);
    console.log('Session Storage Available:', !!window.sessionStorage);
  }
  
  console.groupEnd();
};

export const clearFirebaseCache = () => {
  if (typeof window !== 'undefined') {
    // Clear localStorage
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('firebase:')) {
        localStorage.removeItem(key);
      }
    });
    
    // Clear sessionStorage
    Object.keys(sessionStorage).forEach(key => {
      if (key.startsWith('firebase:')) {
        sessionStorage.removeItem(key);
      }
    });
    
    console.log('🧹 Firebase cache cleared');
  }
};

export const checkFirebaseRequirements = () => {
  const requirements = {
    projectId: !!auth.app.options.projectId,
    authDomain: !!auth.app.options.authDomain,
    apiKey: !!auth.app.options.apiKey,
    https: typeof window !== 'undefined' ? window.location.protocol === 'https:' || window.location.hostname === 'localhost' : true,
    localStorage: typeof window !== 'undefined' ? !!window.localStorage : true,
    cookies: typeof window !== 'undefined' ? navigator.cookieEnabled : true
  };
  
  console.table(requirements);
  
  const allGood = Object.values(requirements).every(Boolean);
  console.log(allGood ? '✅ All requirements met' : '❌ Some requirements not met');
  
  return requirements;
};

export const getFirebaseErrorHelp = (errorCode: string) => {
  const errorHelp: Record<string, string> = {
    'auth/internal-error': `
🔧 Internal Error - Usually a configuration issue:
• Check that Phone authentication is enabled in Firebase Console
• Verify authorized domains include localhost and 127.0.0.1
• Ensure billing is enabled (Blaze plan required for SMS)
• Check reCAPTCHA configuration
• Try clearing browser cache and cookies
    `,
    'auth/invalid-phone-number': `
📱 Invalid Phone Number:
• Use international format: +1234567890
• Include country code
• Remove spaces, dashes, parentheses
    `,
    'auth/too-many-requests': `
⏰ Too Many Requests:
• Wait 15 minutes before trying again
• Use a different phone number for testing
• Check if you're hitting rate limits
    `,
    'auth/captcha-check-failed': `
🤖 reCAPTCHA Failed:
• Solve the reCAPTCHA challenge
• Disable ad blockers
• Try incognito mode
• Clear browser cache
    `,
    'auth/quota-exceeded': `
💰 Quota Exceeded:
• Check Firebase usage limits
• Upgrade billing plan if needed
• Wait for quota reset (usually daily)
    `
  };
  
  return errorHelp[errorCode] || `Unknown error: ${errorCode}`;
};

// Auto-run debug on import in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  console.log('🔥 Firebase Debug Utils Loaded');
  // Uncomment to auto-debug on page load
  // setTimeout(debugFirebaseConfig, 1000);
}
