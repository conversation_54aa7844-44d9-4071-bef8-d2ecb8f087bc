'use client';

import Link from 'next/link';

interface FooterProps {
  className?: string;
}

export default function Footer({ className = '' }: FooterProps) {
  return (
    <footer className={`glass-card py-12 border-t border-gray-600/30 ${className}`}>
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          {/* Company Info */}
          <div>
            <Link href="/" className="flex items-center mb-4">
              <div className="text-yellow-400 text-2xl mr-2">
                <i className="fas fa-comment-dollar"></i>
              </div>
              <span className="font-bold text-xl gold-gradient">BoGuani</span>
            </Link>
            <p className="text-gray-400 mb-4">Messenger of Value</p>
            <p className="text-gray-400 text-sm leading-relaxed">
              Where ancient wisdom meets modern technology. Experience the future of value-based communication.
            </p>
          </div>

          {/* Product Links */}
          <div>
            <h3 className="font-semibold mb-4 text-yellow-400">Product</h3>
            <ul className="space-y-2 text-gray-400">
              <li><a href="#features" className="hover:text-yellow-400 transition-colors">Features</a></li>
              <li><Link href="/security" className="hover:text-yellow-400 transition-colors">Security</Link></li>
              <li><Link href="/pricing" className="hover:text-yellow-400 transition-colors">Pricing</Link></li>
              <li><Link href="/downloads" className="hover:text-yellow-400 transition-colors">Downloads</Link></li>
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="font-semibold mb-4 text-yellow-400">Company</h3>
            <ul className="space-y-2 text-gray-400">
              <li><a href="#about" className="hover:text-yellow-400 transition-colors">About</a></li>
              <li><Link href="/careers" className="hover:text-yellow-400 transition-colors">Careers</Link></li>
              <li><Link href="/blog" className="hover:text-yellow-400 transition-colors">Blog</Link></li>
              <li><a href="#contact" className="hover:text-yellow-400 transition-colors">Contact</a></li>
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 className="font-semibold mb-4 text-yellow-400">Support</h3>
            <ul className="space-y-2 text-gray-400">
              <li><Link href="/support" className="hover:text-yellow-400 transition-colors">Help Center</Link></li>
              <li><Link href="/guides" className="hover:text-yellow-400 transition-colors">Guides</Link></li>
              <li><Link href="/api-docs" className="hover:text-yellow-400 transition-colors">API Docs</Link></li>
              <li><Link href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy</Link></li>
              <li><Link href="/terms" className="hover:text-yellow-400 transition-colors">Terms</Link></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm mb-4 md:mb-0">
            © 2024 BoGuani. All rights reserved.
          </p>
          <div className="flex space-x-6 text-gray-400 text-sm">
            <Link href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy Policy</Link>
            <Link href="/terms" className="hover:text-yellow-400 transition-colors">Terms of Service</Link>
            <Link href="/security" className="hover:text-yellow-400 transition-colors">Security</Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
