# BoGuani Production Deployment Guide

## Prerequisites

1. **Firebase Project Setup**
   - Create a Firebase project at https://console.firebase.google.com
   - Enable Authentication with Phone provider
   - Set up Firestore database
   - Deploy Firestore security rules

2. **Plaid Account Setup**
   - Sign up at https://plaid.com
   - Get your Client ID and Secret keys
   - Configure webhook endpoints

3. **Twilio Account Setup** (Optional - for custom SMS)
   - Sign up at https://twilio.com
   - Get Account SID, Auth Token, and Phone Number

## Environment Variables

Set these environment variables in your deployment platform:

### Firebase Configuration
```
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id
```

### Authentication & Security
```
JWT_SECRET=your_super_secure_jwt_secret_key
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=https://your-domain.com
ENCRYPTION_KEY=your_encryption_key
MESSAGE_ENCRYPTION_KEY=your_message_encryption_key
```

### Plaid Configuration
```
PLAID_CLIENT_ID=your_plaid_client_id
PLAID_SECRET=your_plaid_secret
PLAID_ENCRYPTION_KEY=your_plaid_encryption_key
PLAID_ENV=production
```

### Twilio Configuration (Optional)
```
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
```

### App Configuration
```
NEXT_PUBLIC_APP_URL=https://your-domain.com
NODE_ENV=production
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000
```

## Deployment Steps

### 1. Deploy to Vercel

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

### 2. Configure Environment Variables in Vercel

```bash
# Set environment variables
vercel env add NEXT_PUBLIC_FIREBASE_API_KEY
vercel env add NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
# ... add all other environment variables
```

### 3. Deploy Firestore Security Rules

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Deploy security rules
firebase deploy --only firestore:rules
```

### 4. Configure Custom Domain (Optional)

1. Go to Vercel dashboard
2. Add your custom domain
3. Configure DNS records
4. Enable SSL certificate

## Post-Deployment Configuration

### 1. Firebase Authentication
- Configure authorized domains in Firebase Console
- Add your production domain to authorized domains

### 2. Plaid Webhook Configuration
- Set webhook URL to: `https://your-domain.com/api/plaid/webhook`
- Configure webhook events in Plaid dashboard

### 3. Security Headers
- Verify CSP headers are working
- Test HTTPS redirect
- Check security headers with tools like securityheaders.com

### 4. Performance Optimization
- Enable Vercel Analytics
- Configure caching headers
- Optimize images and assets

## Monitoring & Maintenance

### 1. Error Monitoring
- Set up Sentry or similar error tracking
- Monitor API endpoints
- Set up alerts for critical errors

### 2. Performance Monitoring
- Use Vercel Analytics
- Monitor Core Web Vitals
- Set up uptime monitoring

### 3. Security Monitoring
- Regular security audits
- Monitor for suspicious activity
- Keep dependencies updated

## Troubleshooting

### Common Issues

1. **Firebase Connection Issues**
   - Verify environment variables
   - Check Firebase project configuration
   - Ensure authorized domains are set

2. **Plaid Integration Issues**
   - Verify Plaid credentials
   - Check webhook configuration
   - Test in sandbox mode first

3. **Authentication Problems**
   - Check JWT secret configuration
   - Verify phone authentication setup
   - Test SMS delivery

### Support

For deployment support:
1. Check Vercel documentation
2. Review Firebase documentation
3. Contact support teams if needed

## Security Checklist

- [ ] All environment variables are set
- [ ] HTTPS is enforced
- [ ] Security headers are configured
- [ ] Firestore rules are deployed
- [ ] Rate limiting is active
- [ ] Error monitoring is set up
- [ ] Regular security audits scheduled
