# BoGuani Production Deployment Guide

## Prerequisites

1. **Firebase Project Setup**
   - Create a Firebase project at https://console.firebase.google.com
   - Enable Authentication with Phone provider
   - Set up Firestore database
   - Deploy Firestore security rules

2. **Plaid Account Setup**
   - Sign up at https://plaid.com
   - Get your Client ID and Secret keys
   - Configure webhook endpoints

3. **Twilio Account Setup** (Optional - for custom SMS)
   - Sign up at https://twilio.com
   - Get Account SID, Auth Token, and Phone Number

## Environment Variables

Set these environment variables in your deployment platform:

### Firebase Configuration
```
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id
```

### Authentication & Security
```
JWT_SECRET=your_super_secure_jwt_secret_key
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=https://your-domain.com
ENCRYPTION_KEY=your_encryption_key
MESSAGE_ENCRYPTION_KEY=your_message_encryption_key
```

### Plaid Configuration
```
PLAID_CLIENT_ID=your_plaid_client_id
PLAID_SECRET=your_plaid_secret
PLAID_ENCRYPTION_KEY=your_plaid_encryption_key
PLAID_ENV=production
```

### Twilio Configuration (Optional)
```
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
```

### App Configuration
```
NEXT_PUBLIC_APP_URL=https://your-domain.com
NODE_ENV=production
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000
```

## Deployment Steps

### 1. Deploy to Heroku

```bash
# Install Heroku CLI (if not already installed)
# Download from: https://devcenter.heroku.com/articles/heroku-cli

# Login to Heroku
heroku login

# Create Heroku app
heroku create your-boguani-app-name

# Add Node.js buildpack
heroku buildpacks:set heroku/nodejs

# Deploy
git add .
git commit -m "Deploy BoGuani to Heroku"
git push heroku main
```

### 2. Configure Environment Variables in Heroku

```bash
# Set Firebase environment variables
heroku config:set NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
heroku config:set NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=chatpay-4922e.firebaseapp.com
heroku config:set NEXT_PUBLIC_FIREBASE_PROJECT_ID=chatpay-4922e
heroku config:set NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=chatpay-4922e.appspot.com
heroku config:set NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=211600276697
heroku config:set NEXT_PUBLIC_FIREBASE_APP_ID=1:211600276697:web:a94ed4b6baaf7a654492c8
heroku config:set NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-GGBK4R7EFV

# Set authentication secrets
heroku config:set JWT_SECRET=your_super_secure_jwt_secret_key
heroku config:set NEXTAUTH_SECRET=your_nextauth_secret
heroku config:set ENCRYPTION_KEY=your_encryption_key
heroku config:set MESSAGE_ENCRYPTION_KEY=your_message_encryption_key

# Set Plaid configuration
heroku config:set PLAID_CLIENT_ID=your_plaid_client_id
heroku config:set PLAID_SECRET=your_plaid_secret
heroku config:set PLAID_ENCRYPTION_KEY=your_plaid_encryption_key
heroku config:set PLAID_ENV=production

# Set Twilio configuration (optional)
heroku config:set TWILIO_ACCOUNT_SID=your_twilio_account_sid
heroku config:set TWILIO_AUTH_TOKEN=your_twilio_auth_token
heroku config:set TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Set app configuration
heroku config:set NODE_ENV=production
heroku config:set RATE_LIMIT_MAX=100
heroku config:set RATE_LIMIT_WINDOW=900000
```

### 3. Deploy Firestore Security Rules

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Deploy security rules
firebase deploy --only firestore:rules
```

### 4. Configure Custom Domain (Optional)

```bash
# Add custom domain to Heroku
heroku domains:add your-domain.com

# Configure DNS records (point to Heroku)
# Add CNAME record: your-domain.com -> your-app-name.herokuapp.com

# Enable SSL certificate
heroku certs:auto:enable
```

## Post-Deployment Configuration

### 1. Firebase Authentication
- Configure authorized domains in Firebase Console
- Add your production domain to authorized domains

### 2. Plaid Webhook Configuration
- Set webhook URL to: `https://your-app-name.herokuapp.com/api/plaid/webhook`
- Configure webhook events in Plaid dashboard

### 3. Security Headers
- Verify CSP headers are working
- Test HTTPS redirect (automatic on Heroku)
- Check security headers with tools like securityheaders.com

### 4. Performance Optimization
- Monitor with Heroku metrics
- Configure caching headers
- Optimize images and assets
- Consider Heroku Redis for session storage

## Monitoring & Maintenance

### 1. Error Monitoring
- Set up Sentry or similar error tracking
- Monitor API endpoints
- Set up alerts for critical errors

### 2. Performance Monitoring
- Use Heroku Metrics dashboard
- Monitor dyno performance and response times
- Set up uptime monitoring (Pingdom, UptimeRobot)
- Consider New Relic or Datadog for advanced monitoring

### 3. Security Monitoring
- Regular security audits
- Monitor for suspicious activity
- Keep dependencies updated

## Troubleshooting

### Common Issues

1. **Firebase Connection Issues**
   - Verify environment variables
   - Check Firebase project configuration
   - Ensure authorized domains are set

2. **Plaid Integration Issues**
   - Verify Plaid credentials
   - Check webhook configuration
   - Test in sandbox mode first

3. **Authentication Problems**
   - Check JWT secret configuration
   - Verify phone authentication setup
   - Test SMS delivery

### Support

For deployment support:
1. Check Heroku documentation: https://devcenter.heroku.com/
2. Review Firebase documentation
3. Heroku support: https://help.heroku.com/
4. Contact support teams if needed

## Security Checklist

- [ ] All environment variables are set
- [ ] HTTPS is enforced
- [ ] Security headers are configured
- [ ] Firestore rules are deployed
- [ ] Rate limiting is active
- [ ] Error monitoring is set up
- [ ] Regular security audits scheduled
