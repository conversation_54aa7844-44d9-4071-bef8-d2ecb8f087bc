import { NextRequest, NextResponse } from 'next/server';
import { collection, addDoc, getDocs, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export async function GET(request: NextRequest) {
  try {
    // Test reading from Firestore
    const testCollection = collection(db, 'test');
    const snapshot = await getDocs(testCollection);
    
    const documents = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    return NextResponse.json({
      success: true,
      message: 'Firestore connection successful',
      documentsCount: documents.length,
      documents: documents
    });

  } catch (error: any) {
    console.error('Firestore test error:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to connect to Firestore',
      details: error.code || 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Test writing to Firestore
    const testData = {
      message: 'Hello from BoGuani!',
      timestamp: serverTimestamp(),
      testId: Math.random().toString(36).substr(2, 9)
    };

    const docRef = await addDoc(collection(db, 'test'), testData);

    return NextResponse.json({
      success: true,
      message: 'Test document created successfully',
      documentId: docRef.id
    });

  } catch (error: any) {
    console.error('Firestore write test error:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to write to Firestore',
      details: error.code || 'Unknown error'
    }, { status: 500 });
  }
}
