# 🚀 BoGuani Heroku Deployment Guide

## Quick Heroku Deployment

### Prerequisites
- Heroku CLI installed: https://devcenter.heroku.com/articles/heroku-cli
- Git repository initialized
- Firebase project set up (already done: `chatpay-4922e`)

### 1. Create Heroku App

```bash
# Login to Heroku
heroku login

# Create your app (replace with your preferred name)
heroku create boguani-messenger

# Add Node.js buildpack
heroku buildpacks:set heroku/nodejs
```

### 2. Set Environment Variables

Copy and paste these commands (replace values as needed):

```bash
# Firebase Configuration (already configured)
heroku config:set NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyBqMCY_vblZIApBW5I6aShR1iVQIUtnXJ0
heroku config:set NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=chatpay-4922e.firebaseapp.com
heroku config:set NEXT_PUBLIC_FIREBASE_PROJECT_ID=chatpay-4922e
heroku config:set NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=chatpay-4922e.appspot.com
heroku config:set NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
heroku config:set NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:a94ed4b6baaf7a654492c8
heroku config:set NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-GGBK4R7EFV

# Security Keys (generate new ones for production)
heroku config:set JWT_SECRET=$(openssl rand -base64 32)
heroku config:set NEXTAUTH_SECRET=$(openssl rand -base64 32)
heroku config:set ENCRYPTION_KEY=$(openssl rand -base64 32)
heroku config:set MESSAGE_ENCRYPTION_KEY=$(openssl rand -base64 32)

# App Configuration
heroku config:set NODE_ENV=production
heroku config:set RATE_LIMIT_MAX=100
heroku config:set RATE_LIMIT_WINDOW=900000

# Plaid Configuration (get from Plaid dashboard)
heroku config:set PLAID_CLIENT_ID=your_plaid_client_id
heroku config:set PLAID_SECRET=your_plaid_secret_key
heroku config:set PLAID_ENCRYPTION_KEY=$(openssl rand -base64 32)
heroku config:set PLAID_ENV=sandbox  # Change to 'production' when ready

# Twilio Configuration (optional - for custom SMS)
heroku config:set TWILIO_ACCOUNT_SID=**********************************
heroku config:set TWILIO_AUTH_TOKEN=09f5e32c69ee3131b553a3caee157bd8
heroku config:set TWILIO_PHONE_NUMBER=+***********
```

### 3. Deploy to Heroku

```bash
# Add all files to git
git add .

# Commit changes
git commit -m "Deploy BoGuani to Heroku"

# Deploy to Heroku
git push heroku main

# Open your app
heroku open
```

### 4. Configure Firebase for Production

After deployment, update Firebase settings:

1. **Add Heroku domain to Firebase**:
   - Go to Firebase Console → Authentication → Settings
   - Add your Heroku domain: `https://your-app-name.herokuapp.com`

2. **Update CORS settings**:
   ```bash
   heroku config:set NEXT_PUBLIC_APP_URL=https://your-app-name.herokuapp.com
   ```

### 5. Enable Phone Authentication

1. Go to Firebase Console → Authentication → Sign-in method
2. Enable "Phone" provider
3. Add your Heroku domain to authorized domains

### 6. Test Your Production App

1. Visit your Heroku app URL
2. Go to `/auth` page
3. Test SMS verification with real phone number
4. Create profile and test messaging

## Heroku-Specific Features

### Automatic SSL
- Heroku provides automatic SSL certificates
- Your app will be available at `https://your-app-name.herokuapp.com`

### Environment Variables Management
```bash
# View all config vars
heroku config

# Set a single config var
heroku config:set KEY=value

# Remove a config var
heroku config:unset KEY
```

### Logs and Monitoring
```bash
# View logs
heroku logs --tail

# View metrics
heroku ps

# Scale dynos
heroku ps:scale web=1
```

### Database Add-ons (Optional)
```bash
# Add Redis for session storage
heroku addons:create heroku-redis:mini

# Add PostgreSQL for additional data
heroku addons:create heroku-postgresql:mini
```

## Production Checklist

- [ ] Environment variables set
- [ ] Firebase domain authorized
- [ ] Phone authentication enabled
- [ ] SSL certificate active (automatic)
- [ ] Custom domain configured (optional)
- [ ] Monitoring set up
- [ ] Error tracking configured

## Troubleshooting

### Common Issues

1. **Build Failures**:
   ```bash
   # Check build logs
   heroku logs --tail
   
   # Rebuild
   git commit --allow-empty -m "Rebuild"
   git push heroku main
   ```

2. **Environment Variable Issues**:
   ```bash
   # Check all config vars
   heroku config
   
   # Restart app after config changes
   heroku restart
   ```

3. **Firebase Connection Issues**:
   - Verify all Firebase config vars are set
   - Check Firebase console for authorized domains
   - Ensure Firestore rules are deployed

### Support
- Heroku Support: https://help.heroku.com/
- Firebase Support: https://firebase.google.com/support/
- BoGuani Issues: Check deployment logs

## Cost Optimization

### Free Tier Usage
- Heroku free tier: 550-1000 dyno hours/month
- Firebase free tier: 50K reads, 20K writes/day
- Plaid sandbox: Free for development

### Scaling for Production
```bash
# Upgrade to paid dyno for 24/7 uptime
heroku ps:scale web=1:standard-1x

# Add monitoring
heroku addons:create newrelic:wayne
```

Your BoGuani app is now ready for production on Heroku! 🎉
