import { NextRequest, NextResponse } from 'next/server';
import { plaidService } from '@/lib/plaid';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { rateLimit } from '@/lib/rate-limit';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = await rateLimit(request);
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      );
    }

    const { publicToken, userId } = await request.json();

    if (!publicToken || !userId) {
      return NextResponse.json(
        { error: 'Public token and user ID are required' },
        { status: 400 }
      );
    }

    // Exchange public token for access token
    const accessToken = await plaidService.exchangePublicToken(publicToken);

    // Get account information
    const accounts = await plaidService.getAccounts(accessToken);

    // Store access token securely in Firestore (encrypted in production)
    await updateDoc(doc(db, 'users', userId), {
      plaidAccessToken: accessToken, // In production, encrypt this
      bankAccounts: accounts,
      bankConnected: true,
      updatedAt: new Date()
    });

    return NextResponse.json({
      success: true,
      accounts,
      message: 'Bank account connected successfully'
    });

  } catch (error: any) {
    console.error('Exchange token error:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to connect bank account' },
      { status: 500 }
    );
  }
}
