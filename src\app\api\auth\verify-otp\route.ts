import { NextRequest, NextResponse } from 'next/server';
import { authService } from '@/lib/auth';
import { rateLimit } from '@/lib/rate-limit';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = await rateLimit(request);
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      );
    }

    const { phoneNumber, code, sessionId } = await request.json();

    // Validate inputs
    if (!phoneNumber || !code || !sessionId) {
      return NextResponse.json(
        { error: 'Phone number, verification code, and session ID are required' },
        { status: 400 }
      );
    }

    // Validate code format
    if (!/^\d{6}$/.test(code)) {
      return NextResponse.json(
        { error: 'Invalid verification code format' },
        { status: 400 }
      );
    }

    // For demo purposes, accept any 6-digit code
    // In production, you'd verify against the actual sent code
    if (code === '123456' || code.length === 6) {
      // Generate a temporary user token
      const userToken = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      return NextResponse.json({
        success: true,
        message: 'Phone number verified successfully',
        userToken,
        phoneNumber
      });
    } else {
      return NextResponse.json(
        { error: 'Invalid verification code' },
        { status: 400 }
      );
    }

  } catch (error: any) {
    console.error('Verify OTP error:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to verify code' },
      { status: 500 }
    );
  }
}
