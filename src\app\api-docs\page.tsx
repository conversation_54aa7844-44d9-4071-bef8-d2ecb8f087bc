'use client';

import Link from 'next/link';
import { useState } from 'react';
import Layout from '@/components/Layout';

export default function ApiDocsPage() {
  const [activeSection, setActiveSection] = useState('getting-started');

  return (
    <Layout>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');
        
        body {
          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);
          font-family: 'Montserrat', sans-serif;
        }
        
        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }
        
        .gold-border {
          border: 2px solid transparent;
          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,
                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;
        }
        
        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
      `}</style>

      {/* Hero Section */}
      <section className="py-16">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h1 className="text-5xl lg:text-6xl font-bold mb-6 gold-gradient">API Documentation</h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Build powerful integrations with BoGuani&apos;s secure messaging and payment APIs.
              </p>
            </div>
          </div>
        </section>

        {/* API Docs Content */}
        <section className="py-20 bg-purple-900">
          <div className="container mx-auto px-6">
            <div className="flex flex-col lg:flex-row gap-8">
              
              {/* Sidebar Navigation */}
              <div className="lg:w-1/4">
                <div className="bg-purple-800 rounded-xl p-6 sticky top-24">
                  <h3 className="text-xl font-semibold mb-6 text-yellow-400">Documentation</h3>
                  <nav className="space-y-2">
                    <button
                      onClick={() => setActiveSection('getting-started')}
                      className={`w-full text-left px-4 py-2 rounded-lg transition-colors ${
                        activeSection === 'getting-started' 
                          ? 'bg-yellow-400 text-gray-900' 
                          : 'text-gray-300 hover:text-yellow-400'
                      }`}
                    >
                      Getting Started
                    </button>
                    <button
                      onClick={() => setActiveSection('authentication')}
                      className={`w-full text-left px-4 py-2 rounded-lg transition-colors ${
                        activeSection === 'authentication' 
                          ? 'bg-yellow-400 text-gray-900' 
                          : 'text-gray-300 hover:text-yellow-400'
                      }`}
                    >
                      Authentication
                    </button>
                    <button
                      onClick={() => setActiveSection('messaging')}
                      className={`w-full text-left px-4 py-2 rounded-lg transition-colors ${
                        activeSection === 'messaging' 
                          ? 'bg-yellow-400 text-gray-900' 
                          : 'text-gray-300 hover:text-yellow-400'
                      }`}
                    >
                      Messaging API
                    </button>
                    <button
                      onClick={() => setActiveSection('payments')}
                      className={`w-full text-left px-4 py-2 rounded-lg transition-colors ${
                        activeSection === 'payments' 
                          ? 'bg-yellow-400 text-gray-900' 
                          : 'text-gray-300 hover:text-yellow-400'
                      }`}
                    >
                      Payments API
                    </button>
                    <button
                      onClick={() => setActiveSection('webhooks')}
                      className={`w-full text-left px-4 py-2 rounded-lg transition-colors ${
                        activeSection === 'webhooks' 
                          ? 'bg-yellow-400 text-gray-900' 
                          : 'text-gray-300 hover:text-yellow-400'
                      }`}
                    >
                      Webhooks
                    </button>
                  </nav>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:w-3/4">
                <div className="bg-purple-800 rounded-xl p-8">
                  
                  {/* Getting Started */}
                  {activeSection === 'getting-started' && (
                    <div>
                      <h2 className="text-3xl font-bold mb-6 text-yellow-400">Getting Started</h2>
                      <p className="text-gray-300 mb-6">
                        Welcome to the BoGuani API! Our REST API allows you to integrate secure messaging and payment functionality into your applications.
                      </p>
                      
                      <h3 className="text-xl font-semibold mb-4 text-yellow-400">Base URL</h3>
                      <div className="bg-gray-900 p-4 rounded-lg mb-6">
                        <code className="text-green-400">https://api.boguani.com/v1</code>
                      </div>
                      
                      <h3 className="text-xl font-semibold mb-4 text-yellow-400">Quick Start</h3>
                      <ol className="list-decimal list-inside space-y-2 text-gray-300 mb-6">
                        <li>Sign up for a BoGuani Business account</li>
                        <li>Generate your API keys in the dashboard</li>
                        <li>Make your first API call</li>
                        <li>Start building!</li>
                      </ol>
                      
                      <h3 className="text-xl font-semibold mb-4 text-yellow-400">Example Request</h3>
                      <div className="bg-gray-900 p-4 rounded-lg">
                        <pre className="text-green-400 text-sm">
{`curl -X GET "https://api.boguani.com/v1/user/profile" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json"`}
                        </pre>
                      </div>
                    </div>
                  )}

                  {/* Authentication */}
                  {activeSection === 'authentication' && (
                    <div>
                      <h2 className="text-3xl font-bold mb-6 text-yellow-400">Authentication</h2>
                      <p className="text-gray-300 mb-6">
                        BoGuani uses API keys to authenticate requests. Include your API key in the Authorization header.
                      </p>
                      
                      <h3 className="text-xl font-semibold mb-4 text-yellow-400">API Key Types</h3>
                      <div className="space-y-4 mb-6">
                        <div className="bg-gray-900 p-4 rounded-lg">
                          <h4 className="font-semibold text-yellow-400 mb-2">Public Key</h4>
                          <p className="text-gray-300 text-sm">Used for client-side operations like user authentication</p>
                        </div>
                        <div className="bg-gray-900 p-4 rounded-lg">
                          <h4 className="font-semibold text-yellow-400 mb-2">Secret Key</h4>
                          <p className="text-gray-300 text-sm">Used for server-side operations like sending messages and processing payments</p>
                        </div>
                      </div>
                      
                      <h3 className="text-xl font-semibold mb-4 text-yellow-400">Authentication Header</h3>
                      <div className="bg-gray-900 p-4 rounded-lg">
                        <code className="text-green-400">Authorization: Bearer sk_live_your_secret_key_here</code>
                      </div>
                    </div>
                  )}

                  {/* Messaging API */}
                  {activeSection === 'messaging' && (
                    <div>
                      <h2 className="text-3xl font-bold mb-6 text-yellow-400">Messaging API</h2>
                      <p className="text-gray-300 mb-6">
                        Send and receive encrypted messages programmatically.
                      </p>
                      
                      <h3 className="text-xl font-semibold mb-4 text-yellow-400">Send Message</h3>
                      <div className="bg-gray-900 p-4 rounded-lg mb-6">
                        <pre className="text-green-400 text-sm">
{`POST /v1/messages

{
  "recipient": "+**********",
  "message": "Hello from BoGuani API!",
  "type": "text",
  "encrypted": true
}`}
                        </pre>
                      </div>
                      
                      <h3 className="text-xl font-semibold mb-4 text-yellow-400">Get Messages</h3>
                      <div className="bg-gray-900 p-4 rounded-lg mb-6">
                        <pre className="text-green-400 text-sm">
{`GET /v1/messages?conversation_id=conv_123

Response:
{
  "messages": [
    {
      "id": "msg_456",
      "sender": "+**********",
      "recipient": "+**********",
      "message": "Hello!",
      "timestamp": "2024-01-15T10:30:00Z",
      "encrypted": true
    }
  ]
}`}
                        </pre>
                      </div>
                    </div>
                  )}

                  {/* Payments API */}
                  {activeSection === 'payments' && (
                    <div>
                      <h2 className="text-3xl font-bold mb-6 text-yellow-400">Payments API</h2>
                      <p className="text-gray-300 mb-6">
                        Process secure money transfers within conversations.
                      </p>
                      
                      <h3 className="text-xl font-semibold mb-4 text-yellow-400">Send Payment</h3>
                      <div className="bg-gray-900 p-4 rounded-lg mb-6">
                        <pre className="text-green-400 text-sm">
{`POST /v1/payments

{
  "recipient": "+**********",
  "amount": 50.00,
  "currency": "USD",
  "message": "Lunch money",
  "payment_method": "bank_account"
}`}
                        </pre>
                      </div>
                      
                      <h3 className="text-xl font-semibold mb-4 text-yellow-400">Payment Status</h3>
                      <div className="bg-gray-900 p-4 rounded-lg">
                        <pre className="text-green-400 text-sm">
{`GET /v1/payments/pay_123

Response:
{
  "id": "pay_123",
  "status": "completed",
  "amount": 50.00,
  "currency": "USD",
  "sender": "+**********",
  "recipient": "+**********",
  "created_at": "2024-01-15T10:30:00Z"
}`}
                        </pre>
                      </div>
                    </div>
                  )}

                  {/* Webhooks */}
                  {activeSection === 'webhooks' && (
                    <div>
                      <h2 className="text-3xl font-bold mb-6 text-yellow-400">Webhooks</h2>
                      <p className="text-gray-300 mb-6">
                        Receive real-time notifications about messages and payments.
                      </p>
                      
                      <h3 className="text-xl font-semibold mb-4 text-yellow-400">Webhook Events</h3>
                      <div className="space-y-4 mb-6">
                        <div className="bg-gray-900 p-4 rounded-lg">
                          <h4 className="font-semibold text-yellow-400 mb-2">message.received</h4>
                          <p className="text-gray-300 text-sm">Triggered when a new message is received</p>
                        </div>
                        <div className="bg-gray-900 p-4 rounded-lg">
                          <h4 className="font-semibold text-yellow-400 mb-2">payment.completed</h4>
                          <p className="text-gray-300 text-sm">Triggered when a payment is successfully processed</p>
                        </div>
                        <div className="bg-gray-900 p-4 rounded-lg">
                          <h4 className="font-semibold text-yellow-400 mb-2">payment.failed</h4>
                          <p className="text-gray-300 text-sm">Triggered when a payment fails</p>
                        </div>
                      </div>
                      
                      <h3 className="text-xl font-semibold mb-4 text-yellow-400">Webhook Payload</h3>
                      <div className="bg-gray-900 p-4 rounded-lg">
                        <pre className="text-green-400 text-sm">
{`{
  "event": "message.received",
  "data": {
    "id": "msg_456",
    "sender": "+**********",
    "recipient": "+**********",
    "message": "Hello!",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}`}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </section>


    </Layout>
  );
}
