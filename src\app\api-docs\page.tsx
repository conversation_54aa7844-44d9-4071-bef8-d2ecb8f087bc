'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import Layout from '@/components/Layout';

export default function ApiDocsPage() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">API Documentation</h1>
            <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Build powerful integrations with BoGuani's secure messaging and payment APIs.
            </p>
          </div>
        </div>
      </section>

      {/* API Features */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-t border-gray-600/30">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">API Features</h2>
            <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Everything you need to integrate secure communication and payments
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-comments text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Messaging API</h3>
                <p className="text-gray-300 leading-relaxed">
                  Send and receive encrypted messages with full end-to-end security and real-time delivery.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-credit-card text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Payments API</h3>
                <p className="text-gray-300 leading-relaxed">
                  Process secure payments and money transfers with bank-level encryption and compliance.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-users text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">User Management</h3>
                <p className="text-gray-300 leading-relaxed">
                  Manage user accounts, authentication, and permissions with comprehensive user APIs.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-shield-alt text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Security APIs</h3>
                <p className="text-gray-300 leading-relaxed">
                  Advanced security features including encryption, key management, and audit trails.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-chart-bar text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Analytics API</h3>
                <p className="text-gray-300 leading-relaxed">
                  Get insights into usage patterns, performance metrics, and user engagement data.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                  <i className="fas fa-webhook text-2xl text-gray-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Webhooks</h3>
                <p className="text-gray-300 leading-relaxed">
                  Real-time notifications for events like new messages, payments, and user activities.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Getting Started */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-t border-gray-600/30">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Getting Started</h2>
            <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Start building with BoGuani APIs in minutes
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <motion.div
              className="glass-card p-8 rounded-2xl feature-card professional-shadow text-center"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                <i className="fas fa-code text-2xl text-gray-900"></i>
              </div>
              <h3 className="text-2xl font-bold mb-4 gold-gradient">API Documentation Coming Soon</h3>
              <p className="text-gray-300 leading-relaxed mb-6">
                Our comprehensive API documentation is currently being prepared. Get notified when it's ready!
              </p>
              <Link href="/contact" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                Get Early Access
              </Link>
            </motion.div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
