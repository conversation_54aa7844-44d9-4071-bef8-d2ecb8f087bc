'use client';

import Link from 'next/link';

export default function TermsPage() {
  return (
    <div className="min-h-screen text-white" style={{
      background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)',
      fontFamily: 'Montserrat, sans-serif'
    }}>
      <div className="relative min-h-screen" style={{
        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)',
        backgroundImage: 'radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)'
      }}>
        {/* Navigation */}
        <nav className="fixed w-full z-50 bg-gradient-to-r from-black/80 to-purple-900/80 backdrop-blur-md border-b border-yellow-500/20">
          <div className="container mx-auto px-6 py-4 flex justify-between items-center">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <div className="text-yellow-500 text-3xl mr-3">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                  BoGuani
                </span>
              </Link>
            </div>
            <div className="flex space-x-6">
              <Link href="/" className="hover:text-yellow-400 transition-colors">Home</Link>
              <Link href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy</Link>
              <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-4 py-2 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all">
                Open Web App
              </Link>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <div className="pt-24 pb-16 px-6">
          <div className="container mx-auto max-w-4xl">
            {/* Header */}
            <div className="text-center mb-16">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                <i className="fas fa-file-contract text-3xl text-purple-900"></i>
              </div>
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                Terms of Service
              </h1>
              <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                Please read these terms carefully before using BoGuani. By using our service, you agree to these terms.
              </p>
              <p className="text-sm text-gray-400 mt-4">Last updated: January 1, 2024</p>
            </div>

            {/* Terms Content */}
            <div className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg rounded-2xl border border-yellow-500/20 p-8 space-y-8">
              
              {/* Acceptance */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">1. Acceptance of Terms</h2>
                <p className="text-gray-300 leading-relaxed">
                  By accessing or using BoGuani, you agree to be bound by these Terms of Service and all applicable laws and regulations. If you do not agree with any of these terms, you are prohibited from using or accessing this service.
                </p>
              </section>

              {/* Description of Service */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">2. Description of Service</h2>
                <p className="text-gray-300 leading-relaxed mb-4">
                  BoGuani is a secure messaging platform that enables users to send encrypted messages and transfer value instantly. Our service includes:
                </p>
                <ul className="space-y-2 text-gray-300">
                  <li className="flex items-start">
                    <i className="fas fa-check text-yellow-400 mr-3 mt-1"></i>
                    <span>End-to-end encrypted messaging</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check text-yellow-400 mr-3 mt-1"></i>
                    <span>Secure money transfers</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check text-yellow-400 mr-3 mt-1"></i>
                    <span>Group messaging and payments</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check text-yellow-400 mr-3 mt-1"></i>
                    <span>Contact management and verification</span>
                  </li>
                </ul>
              </section>

              {/* User Responsibilities */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">3. User Responsibilities</h2>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold text-yellow-400 mb-2">Account Security</h3>
                    <p className="text-gray-300">You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-yellow-400 mb-2">Lawful Use</h3>
                    <p className="text-gray-300">You agree to use BoGuani only for lawful purposes and in accordance with these Terms of Service.</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-yellow-400 mb-2">Accurate Information</h3>
                    <p className="text-gray-300">You agree to provide accurate, current, and complete information during registration and to update such information as necessary.</p>
                  </div>
                </div>
              </section>

              {/* Prohibited Activities */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">4. Prohibited Activities</h2>
                <p className="text-gray-300 mb-4">You agree not to engage in any of the following prohibited activities:</p>
                <ul className="space-y-2 text-gray-300">
                  <li className="flex items-start">
                    <i className="fas fa-times text-red-400 mr-3 mt-1"></i>
                    <span>Using the service for illegal activities or fraud</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-times text-red-400 mr-3 mt-1"></i>
                    <span>Attempting to gain unauthorized access to other accounts</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-times text-red-400 mr-3 mt-1"></i>
                    <span>Transmitting malware, viruses, or harmful code</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-times text-red-400 mr-3 mt-1"></i>
                    <span>Harassing, threatening, or abusing other users</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-times text-red-400 mr-3 mt-1"></i>
                    <span>Violating any applicable laws or regulations</span>
                  </li>
                </ul>
              </section>

              {/* Payment Terms */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">5. Payment Terms</h2>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold text-yellow-400 mb-2">Transaction Fees</h3>
                    <p className="text-gray-300">BoGuani may charge fees for certain transactions. All applicable fees will be clearly disclosed before you complete a transaction.</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-yellow-400 mb-2">Payment Processing</h3>
                    <p className="text-gray-300">Payments are processed through secure third-party payment processors. You agree to their terms and conditions.</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-yellow-400 mb-2">Refunds</h3>
                    <p className="text-gray-300">Refunds are handled on a case-by-case basis. Contact our support team for assistance with payment issues.</p>
                  </div>
                </div>
              </section>

              {/* Privacy and Data */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">6. Privacy and Data Protection</h2>
                <p className="text-gray-300 leading-relaxed">
                  Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your information. By using BoGuani, you also agree to our Privacy Policy.
                </p>
              </section>

              {/* Limitation of Liability */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">7. Limitation of Liability</h2>
                <p className="text-gray-300 leading-relaxed">
                  BoGuani is provided &quot;as is&quot; without warranties of any kind. We shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use of the service.
                </p>
              </section>

              {/* Termination */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">8. Termination</h2>
                <p className="text-gray-300 leading-relaxed">
                  We may terminate or suspend your account immediately, without prior notice, for conduct that we believe violates these Terms of Service or is harmful to other users, us, or third parties.
                </p>
              </section>

              {/* Changes to Terms */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">9. Changes to Terms</h2>
                <p className="text-gray-300 leading-relaxed">
                  We reserve the right to modify these terms at any time. We will notify users of any material changes through the app or by email. Your continued use of BoGuani after such changes constitutes acceptance of the new terms.
                </p>
              </section>

              {/* Contact Information */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">10. Contact Information</h2>
                <p className="text-gray-300 mb-4">
                  If you have questions about these Terms of Service, please contact us:
                </p>
                <div className="bg-purple-900/50 p-4 rounded-lg border border-yellow-400/20">
                  <p className="text-gray-300">
                    <strong>Email:</strong> <EMAIL><br />
                    <strong>Address:</strong> BoGuani Legal Team, 123 Terms Street, Legal City, LC 12345
                  </p>
                </div>
              </section>
            </div>

            {/* Footer Actions */}
            <div className="text-center mt-12">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/privacy" className="border-2 border-yellow-400 text-yellow-400 px-6 py-3 rounded-full font-semibold hover:bg-yellow-400 hover:text-purple-900 transition-all">
                  Read Privacy Policy
                </Link>
                <Link href="/contact" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all">
                  Contact Legal Team
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
