'use client';

import { useEffect, useState } from 'react';
import { auth, checkFirebaseConfig } from '@/lib/firebase';
import { RecaptchaVerifier, signInWithPhoneNumber } from 'firebase/auth';
import Layout from '@/components/Layout';

export default function FirebaseTestPage() {
  const [configStatus, setConfigStatus] = useState<any>(null);
  const [authStatus, setAuthStatus] = useState('');
  const [recaptchaStatus, setRecaptchaStatus] = useState('');
  const [testPhone, setTestPhone] = useState('+1234567890');
  const [testResult, setTestResult] = useState('');

  useEffect(() => {
    // Check Firebase configuration
    const config = checkFirebaseConfig();
    setConfigStatus(config);

    // Check auth status
    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user) {
        setAuthStatus(`Authenticated as: ${user.uid}`);
      } else {
        setAuthStatus('Not authenticated');
      }
    });

    return () => unsubscribe();
  }, []);

  const testRecaptcha = async () => {
    try {
      setRecaptchaStatus('Testing reCAPTCHA...');
      
      // Clear any existing reCAPTCHA
      const container = document.getElementById('test-recaptcha');
      if (container) {
        container.innerHTML = '';
      }

      // Create new reCAPTCHA
      const verifier = new RecaptchaVerifier(auth, 'test-recaptcha', {
        size: 'normal',
        callback: () => {
          setRecaptchaStatus('✅ reCAPTCHA solved successfully!');
        },
        'expired-callback': () => {
          setRecaptchaStatus('❌ reCAPTCHA expired');
        }
      });

      await verifier.render();
      setRecaptchaStatus('✅ reCAPTCHA rendered successfully. Please solve it.');
      
    } catch (error: any) {
      setRecaptchaStatus(`❌ reCAPTCHA error: ${error.message}`);
      console.error('reCAPTCHA test error:', error);
    }
  };

  const testPhoneAuth = async () => {
    try {
      setTestResult('Testing phone authentication...');
      
      // Create reCAPTCHA first
      const container = document.getElementById('phone-test-recaptcha');
      if (container) {
        container.innerHTML = '';
      }

      const verifier = new RecaptchaVerifier(auth, 'phone-test-recaptcha', {
        size: 'normal'
      });

      await verifier.render();
      
      // Try to send SMS (this will likely fail with test number, but we can see the error)
      const result = await signInWithPhoneNumber(auth, testPhone, verifier);
      setTestResult('✅ Phone auth test successful (SMS would be sent)');
      
    } catch (error: any) {
      setTestResult(`Phone auth test result: ${error.code} - ${error.message}`);
      console.error('Phone auth test error:', error);
    }
  };

  return (
    <Layout>
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Firebase Configuration Test</h1>
            <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Test your Firebase configuration and phone authentication setup
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-8">
            {/* Configuration Status */}
            <div className="glass-card p-8 rounded-2xl">
              <h2 className="text-2xl font-bold mb-4 gold-gradient">Configuration Status</h2>
              {configStatus && (
                <div className="space-y-2">
                  {Object.entries(configStatus).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-gray-300">{key}:</span>
                      <span className={value ? 'text-green-400' : 'text-red-400'}>
                        {value ? '✅ Configured' : '❌ Missing'}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Auth Status */}
            <div className="glass-card p-8 rounded-2xl">
              <h2 className="text-2xl font-bold mb-4 gold-gradient">Authentication Status</h2>
              <p className="text-gray-300">{authStatus}</p>
            </div>

            {/* reCAPTCHA Test */}
            <div className="glass-card p-8 rounded-2xl">
              <h2 className="text-2xl font-bold mb-4 gold-gradient">reCAPTCHA Test</h2>
              <button 
                onClick={testRecaptcha}
                className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all mb-4"
              >
                Test reCAPTCHA
              </button>
              <p className="text-gray-300 mb-4">{recaptchaStatus}</p>
              <div id="test-recaptcha"></div>
            </div>

            {/* Phone Auth Test */}
            <div className="glass-card p-8 rounded-2xl">
              <h2 className="text-2xl font-bold mb-4 gold-gradient">Phone Authentication Test</h2>
              <div className="mb-4">
                <input
                  type="text"
                  value={testPhone}
                  onChange={(e) => setTestPhone(e.target.value)}
                  className="w-full p-3 rounded-lg bg-gray-800 border border-gray-600 text-white"
                  placeholder="Test phone number"
                />
              </div>
              <button 
                onClick={testPhoneAuth}
                className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all mb-4"
              >
                Test Phone Auth
              </button>
              <p className="text-gray-300 mb-4">{testResult}</p>
              <div id="phone-test-recaptcha"></div>
            </div>

            {/* Instructions */}
            <div className="glass-card p-8 rounded-2xl">
              <h2 className="text-2xl font-bold mb-4 gold-gradient">Firebase Console Checklist</h2>
              <div className="space-y-2 text-gray-300">
                <p>✅ 1. Go to <a href="https://console.firebase.google.com/project/chatpay-4922e/authentication/providers" target="_blank" className="text-yellow-400 hover:underline">Firebase Console - Authentication</a></p>
                <p>✅ 2. Enable Phone authentication provider</p>
                <p>✅ 3. Go to Authentication → Settings → Authorized domains</p>
                <p>✅ 4. Add: localhost, 127.0.0.1, your-domain.com</p>
                <p>✅ 5. Check reCAPTCHA configuration in Authentication → Settings</p>
                <p>✅ 6. Ensure your project has billing enabled (required for SMS)</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
